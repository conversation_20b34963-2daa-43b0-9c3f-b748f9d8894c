using GGEC.AOI.T68ZJ.Config;
using GGEC.AOI.T68ZJ.Log;
using GGEC.AOI.T68ZJ.Managers;

namespace GGEC.AOI.T68ZJ.Test
{
    /// <summary>
    /// 配置系统测试类
    /// </summary>
    public static class ConfigTest
    {
        /// <summary>
        /// 测试配置加载和保存
        /// </summary>
        public static void TestConfigSystem()
        {
            try
            {
                Logger.Info("开始测试配置系统...");

                // 测试加载配置
                var config = AppConfig.LoadConfig();
                Logger.Info($"配置加载成功:");
                Logger.Info($"  相机管理器类型: {config.CameraManager.Type}");
                Logger.Info($"  相机曝光时间: {config.Camera.ExposureTime}");
                Logger.Info($"  相机增益: {config.Camera.Gain}");
                Logger.Info($"  传送带速度: {config.Conveyor.Speed}");
                Logger.Info($"  MQTT服务器: {config.Mqtt.ServerEndpoint}:{config.Mqtt.ServerPort}");

                // 测试修改配置
                config.Camera.ExposureTime = 3000.0;
                config.Camera.Gain = 25.0;
                config.CameraManager.SetManagerType(CameraManagerFactory.CameraManagerType.Enhanced);

                // 测试保存配置
                AppConfig.SaveConfig(config);
                Logger.Info("配置保存成功");

                // 测试重新加载配置
                AppConfig.ReloadConfig();
                var reloadedConfig = AppConfig.LoadConfig();
                Logger.Info($"重新加载配置:");
                Logger.Info($"  相机管理器类型: {reloadedConfig.CameraManager.Type}");
                Logger.Info($"  相机曝光时间: {reloadedConfig.Camera.ExposureTime}");
                Logger.Info($"  相机增益: {reloadedConfig.Camera.Gain}");

                // 验证配置是否正确保存和加载
                if (reloadedConfig.Camera.ExposureTime == 3000.0 && 
                    reloadedConfig.Camera.Gain == 25.0 &&
                    reloadedConfig.CameraManager.Type == "Enhanced")
                {
                    Logger.Info("✓ 配置系统测试通过");
                }
                else
                {
                    Logger.Error("✗ 配置系统测试失败：配置值不匹配");
                }

                // 测试向后兼容的SimpleConfig
                var inferenceConfig = SimpleConfig.LoadConfig();
                Logger.Info($"推理配置加载成功，模型路径: {inferenceConfig.ModelPath}");

                Logger.Info("配置系统测试完成");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "配置系统测试失败");
            }
        }

        /// <summary>
        /// 重置配置为默认值
        /// </summary>
        public static void ResetToDefaults()
        {
            try
            {
                Logger.Info("重置配置为默认值...");
                var defaultConfig = new ApplicationConfig();
                AppConfig.SaveConfig(defaultConfig);
                Logger.Info("配置已重置为默认值");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "重置配置失败");
            }
        }

        /// <summary>
        /// 显示当前配置信息
        /// </summary>
        public static void ShowCurrentConfig()
        {
            try
            {
                var config = AppConfig.LoadConfig();
                Logger.Info("=== 当前配置信息 ===");
                Logger.Info($"相机管理器类型: {config.CameraManager.Type}");
                Logger.Info($"相机曝光时间: {config.Camera.ExposureTime}μs");
                Logger.Info($"相机增益: {config.Camera.Gain}");
                Logger.Info($"相机像素格式: {config.Camera.PixelFormat}");
                Logger.Info($"传送带速度: {config.Conveyor.Speed}m/s");
                Logger.Info($"触发延迟补偿: {config.Conveyor.TriggerDelayCompensation}ms");
                Logger.Info($"图像输出目录: {config.Image.OutputDirectory}");
                Logger.Info($"JPEG质量: {config.Image.JpegQuality}");
                Logger.Info($"日志级别: {config.Log.Level}");
                Logger.Info($"MQTT服务器: {config.Mqtt.ServerEndpoint}:{config.Mqtt.ServerPort}");
                Logger.Info($"MQTT客户端ID: {config.Mqtt.ClientId}");
                Logger.Info($"推理置信度阈值: {config.Inference.ConfidenceThreshold}");
                Logger.Info("==================");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "显示配置信息失败");
            }
        }
    }
}
