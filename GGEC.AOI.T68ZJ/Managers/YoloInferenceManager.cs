using Compunet.YoloSharp;
using Compunet.YoloSharp.Data;
using Compunet.YoloSharp.Plotting;
using GGEC.AOI.T68ZJ.Extension;
using GGEC.AOI.T68ZJ.Log;
using MvCameraControl;
using OpenCvSharp;
using OpenCvSharp.Extensions;
using SixLabors.ImageSharp.Drawing;
using SixLabors.ImageSharp.PixelFormats;
using System.Diagnostics;
using Path = System.IO.Path;

namespace GGEC.AOI.T68ZJ.Managers
{
    /// <summary>
    /// Yolo推理结果
    /// </summary>
    public class InferenceResult
    {
        public List<KeyValuePair<string, float>> Detections { get; set; } = new List<KeyValuePair<string, float>>();
        public Mat? ProcessedImage { get; set; }
        public double InferenceTime { get; set; }
        public double TotalTime => InferenceTime;
        public string? ResultImagePath { get; set; }
        public List<PinInfo> PinInfos { get; set; } = new List<PinInfo>();
    }


    public class PinInfo
    {
        public PinInfo() { }
        public PinInfo(double Angle)
        {
            this.Angle = Angle;
        }

        public PinInfo(double Angle,double Distance)
        {
            this.Angle = Angle;
            this.Distance = Distance;
        }
        public double Height;
        public double Angle;

        public double Distance;
    }

    /// <summary>
    /// 简化的推理配置
    /// </summary>
    public class InferenceConfig
    {
        public string ModelPath { get; set; } = @".\Resources\Models\t68zj_0723_1145_best.onnx";
        public string SegmentModelPath { get; set; } = @".\Resources\Models\t68zj_pin_0723_1511_best.onnx";
        public string FOKModelPath { get; set; } = @".\Resources\Models\t68zj_fok_0729_1947_best.onnx";
        
        public float ConfidenceThreshold { get; set; } = 0.5f;
        public bool SaveResultImage { get; set; } = true; // 优化：默认关闭结果图像保存
        public bool SaveCroppedDetections { get; set; } = true; // 优化：默认关闭裁剪图像保存
        public bool SaveOriginImage { get; set; } = true; // 优化：新增原图保存开关，默认关闭
        public List<string> CropLabels { get; set; } = new List<string> { "cok2", "fok2" };

        // 性能优化选项
        public bool EnableDetailedTiming { get; set; } = true; // 详细计时开关
        public bool OptimizeImageConversion { get; set; } = true; // 优化图像转换
        public bool SkipUnnecessaryOperations { get; set; } = true; // 跳过不必要操作
    }

    /// <summary>
    /// 简化的Yolo模型推理Helper类
    /// </summary>
    public class YoloInferenceManager : IDisposable
    {
        private readonly InferenceConfig _config;
        private YoloPredictor? _predictor;
        private YoloPredictor? _pinPredictor;
        private YoloPredictor? _fokPredictor;
        //private YoloMaskProcessor? _maskProcessor;

        private readonly string[] _classLabels = ["fng1", "fng2", "fng3", "cng1", "cng2", "cng3", "fok1", "fok2", "fok3", "cok1", "cok2", "cok3"];
        private bool _isInitialized = false;
        private readonly object _lockObject = new object();

        // 常量定义
        private const int REQUIRED_FOK2_DETECTIONS = 2;
        private const double ANGLE_THRESHOLD = 90.0;

        public YoloInferenceManager(InferenceConfig config = null)
        {
            _config = config ?? new InferenceConfig();
            Initialize();
        }

        /// <summary>
        /// 初始化模型
        /// </summary>
        private void Initialize()
        {
            try
            {
                Logger.Info($"初始化YoloSharp，模型：{_config.ModelPath}");

                if (!File.Exists(_config.ModelPath))
                {
                    throw new FileNotFoundException($"模型文件不存在：{_config.ModelPath}");
                }

                _predictor = new YoloPredictor(_config.ModelPath);
                _pinPredictor = new YoloPredictor(_config.SegmentModelPath);
                _fokPredictor = new YoloPredictor(_config.FOKModelPath);
                YoloMaskProcessor.Initialize(_config.SegmentModelPath, areaThreshold: 150);
                _isInitialized = true;
                Logger.Info("YoloSharp初始化完成");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "YoloSharp初始化失败");
                throw;
            }
        }



        /// <summary>
        /// 对相机帧进行推理
        /// </summary>
        public InferenceResult InferFromCameraFrame(IFrameOut frameOut, string outputPrefix = "inference")
        {
            if (!_isInitialized)
                throw new InvalidOperationException("推理引擎未初始化");

            if (frameOut?.Image == null)
                throw new ArgumentNullException(nameof(frameOut), "输入帧为空");

            return InferFromImage(frameOut.Image, outputPrefix);
        }

        /// <summary>
        /// 对相机图像进行推理 - 异步版本
        /// </summary>
        public async Task<InferenceResult> InferFromImageAsync(IImage image, string outputPrefix = "inference")
        {
            if (!_isInitialized)
                throw new InvalidOperationException("推理引擎未初始化");

            if (image == null)
                throw new ArgumentNullException(nameof(image), "输入图像为空");

            return await Task.Run(() =>
            {
                lock (_lockObject)
                {
                    var totalStopwatch = Stopwatch.StartNew();
                    var result = new InferenceResult();

                    try
                    {
                        // 图像转换计时
                        var imageConvertStopwatch = Stopwatch.StartNew();
                        var srcImage = image.ImageToMat();
                        imageConvertStopwatch.Stop();

                        if (_config.EnableDetailedTiming)
                        {
                            Logger.Info($"IImage转Mat耗时：{imageConvertStopwatch.ElapsedMilliseconds}ms");
                        }

                        // 格式转换计时
                        var formatConvertStopwatch = Stopwatch.StartNew();
                        var sharpImage = ConvertMatToImageSharp(srcImage);
                        formatConvertStopwatch.Stop();

                        if (_config.EnableDetailedTiming)
                        {
                            Logger.Info($"Mat转ImageSharp耗时：{formatConvertStopwatch.ElapsedMilliseconds}ms");
                        }

                        // YoloSharp推理计时
                        var inferenceStopwatch = Stopwatch.StartNew();
                        var detectionResult = _predictor.Detect(sharpImage);
                        
                        inferenceStopwatch.Stop();

                        if (_config.EnableDetailedTiming)
                        {
                            Logger.Info($"YoloSharp推理耗时：{inferenceStopwatch.ElapsedMilliseconds}ms");
                        }

                        // 结果处理计时 - 在子线程中异步处理
                        var processStopwatch = Stopwatch.StartNew();
                        ProcessResults(detectionResult, srcImage, result, outputPrefix);
                        processStopwatch.Stop();

                        totalStopwatch.Stop();
                        result.InferenceTime = totalStopwatch.Elapsed.TotalMilliseconds;

                        // 优化：根据配置决定输出详细或简化统计
                        if (_config.EnableDetailedTiming)
                        {
                            string timingReport = $"=== YoloInferenceHelper耗时统计 ===\n" +
                                                $"IImage转Mat：{imageConvertStopwatch.ElapsedMilliseconds}ms\n" +
                                                $"Mat转ImageSharp：{formatConvertStopwatch.ElapsedMilliseconds}ms\n" +
                                                $"YoloSharp推理：{inferenceStopwatch.ElapsedMilliseconds}ms\n" +
                                                $"结果处理：{processStopwatch.ElapsedMilliseconds}ms\n" +
                                                $"总耗时：{totalStopwatch.ElapsedMilliseconds}ms\n" +
                                                $"检测数量：{result.Detections.Count}\n" +
                                                $"================================";

                            Logger.Info(timingReport);
                            Console.WriteLine(timingReport);
                        }
                        else
                        {
                            // 简化输出，只显示关键信息
                            Logger.Info($"推理完成：总耗时{totalStopwatch.ElapsedMilliseconds}ms，检测{result.Detections.Count}个目标");
                        }

                        sharpImage.Dispose();
                        return result;
                    }
                    catch (Exception ex)
                    {
                        totalStopwatch.Stop();
                        Logger.Exception(ex, $"推理异常，总耗时：{totalStopwatch.ElapsedMilliseconds}ms");
                        throw;
                    }
                }
            });
        }

        /// <summary>
        /// 对相机图像进行推理 - 同步版本（保持向后兼容）
        /// </summary>
        public InferenceResult InferFromImage(IImage image, string outputPrefix = "inference")
        {
            return InferFromImageAsync(image, outputPrefix).GetAwaiter().GetResult();
        }

        /// <summary>
        /// 对图像文件进行推理
        /// </summary>
        public InferenceResult InferFromImageFile(string imagePath, string outputPrefix = "inference")
        {
            if (!_isInitialized)
                throw new InvalidOperationException("推理引擎未初始化");

            if (string.IsNullOrEmpty(imagePath))
                throw new ArgumentNullException(nameof(imagePath));

            lock (_lockObject)
            {
                var totalStopwatch = Stopwatch.StartNew();
                var result = new InferenceResult();

                try
                {
                    // 图像加载计时
                    var loadStopwatch = Stopwatch.StartNew();
                    using var srcImage = Cv2.ImRead(imagePath);
                    if (srcImage.Empty())
                        throw new ArgumentException($"无法加载图像：{imagePath}");
                    loadStopwatch.Stop();
                    Logger.Info($"图像文件加载耗时：{loadStopwatch.ElapsedMilliseconds}ms");

                    // 格式转换计时
                    var convertStopwatch = Stopwatch.StartNew();
                    using var sharpImage = ConvertMatToImageSharp(srcImage);
                    convertStopwatch.Stop();
                    Logger.Info($"文件图像格式转换耗时：{convertStopwatch.ElapsedMilliseconds}ms");

                    // 推理计时
                    var inferStopwatch = Stopwatch.StartNew();
                    var detectionResult = _predictor.Detect(sharpImage);

                    inferStopwatch.Stop();
                    Logger.Info($"文件图像推理耗时：{inferStopwatch.ElapsedMilliseconds}ms");

                    // 结果处理计时
                    var processStopwatch = Stopwatch.StartNew();
                    ProcessResults(detectionResult, srcImage, result, outputPrefix);
                    processStopwatch.Stop();
                    Logger.Info($"文件图像结果处理耗时：{processStopwatch.ElapsedMilliseconds}ms");

                    totalStopwatch.Stop();
                    result.InferenceTime = totalStopwatch.Elapsed.TotalMilliseconds;

                    // 输出文件推理耗时统计
                    string fileTimingReport = $"=== 文件推理耗时统计 ===\n" +
                                            $"图像文件：{imagePath}\n" +
                                            $"图像加载：{loadStopwatch.ElapsedMilliseconds}ms\n" +
                                            $"格式转换：{convertStopwatch.ElapsedMilliseconds}ms\n" +
                                            $"推理执行：{inferStopwatch.ElapsedMilliseconds}ms\n" +
                                            $"结果处理：{processStopwatch.ElapsedMilliseconds}ms\n" +
                                            $"总耗时：{totalStopwatch.ElapsedMilliseconds}ms\n" +
                                            $"检测数量：{result.Detections.Count}\n" +
                                            $"=======================";

                    Logger.Info(fileTimingReport);
                    Console.WriteLine(fileTimingReport);

                    return result;
                }
                catch (Exception ex)
                {
                    totalStopwatch.Stop();
                    Logger.Exception(ex, $"图像文件推理异常，总耗时：{totalStopwatch.ElapsedMilliseconds}ms");
                    throw;
                }
            }
        }

        /// <summary>
        /// 转换OpenCV Mat为ImageSharp格式 - 优化版本
        /// </summary>
        private SixLabors.ImageSharp.Image<Rgb24> ConvertMatToImageSharp(Mat mat)
        {
            var convertStopwatch = Stopwatch.StartNew();

            if (_config.EnableDetailedTiming)
            {
                LogMatInfo("输入Mat", mat);
            }

            try
            {
                // 优化：直接使用最快的内存转换方法
                if (_config.OptimizeImageConversion)
                {
                    var result = ConvertViaOptimizedMemory(mat);
                    convertStopwatch.Stop();
                    if (_config.EnableDetailedTiming)
                    {
                        Logger.Info($"优化图像转换耗时：{convertStopwatch.ElapsedMilliseconds}ms");
                    }
                    return result;
                }

                // 原有的多方法尝试逻辑（仅在非优化模式下使用）
                try
                {
                    var result = ConvertSimple(mat);
                    convertStopwatch.Stop();
                    if (_config.EnableDetailedTiming)
                    {
                        Logger.Info($"图像转换成功 - 方法1耗时：{convertStopwatch.ElapsedMilliseconds}ms");
                    }
                    return result;
                }
                catch (Exception ex)
                {
                    Logger.Warn($"方法1转换失败，尝试方法2: {ex.Message}");
                    try
                    {
                        var result = ConvertViaBitmap(mat);
                        convertStopwatch.Stop();
                        if (_config.EnableDetailedTiming)
                        {
                            Logger.Info($"图像转换成功 - 方法2耗时：{convertStopwatch.ElapsedMilliseconds}ms");
                        }
                        return result;
                    }
                    catch (Exception ex2)
                    {
                        Logger.Warn($"方法2转换失败，尝试方法3: {ex2.Message}");
                        var result = ConvertViaMemory(mat);
                        convertStopwatch.Stop();
                        if (_config.EnableDetailedTiming)
                        {
                            Logger.Info($"图像转换成功 - 方法3耗时：{convertStopwatch.ElapsedMilliseconds}ms");
                        }
                        return result;
                    }
                }
            }
            catch (Exception ex)
            {
                convertStopwatch.Stop();
                Logger.Exception(ex, $"图像转换失败，总尝试耗时：{convertStopwatch.ElapsedMilliseconds}ms");
                throw;
            }
        }

        /// <summary>
        /// 简单直接的转换方法（方法1）
        /// </summary>
        private SixLabors.ImageSharp.Image<Rgb24> ConvertSimple(Mat mat)
        {
            // 确保是3通道BGR格式
            Mat bgrMat = EnsureBGR(mat);
            bool needsDispose = bgrMat != mat;

            try
            {
                // 直接使用OpenCvSharp的扩展方法转换为Bitmap
                using var bitmap = bgrMat.ToBitmap();

                // 创建ImageSharp图像，保持原始颜色
                var image = SixLabors.ImageSharp.Image.Load<Rgb24>(
                    new MemoryStream(BitmapToByteArray(bitmap)));

                return image;
            }
            finally
            {
                if (needsDispose)
                    bgrMat.Dispose();
            }
        }

        /// <summary>
        /// 确保Mat是BGR格式
        /// </summary>
        private Mat EnsureBGR(Mat mat)
        {
            if (mat.Channels() == 3)
            {
                return mat; // 假设已经是BGR
            }
            else if (mat.Channels() == 1)
            {
                var bgrMat = new Mat();
                Cv2.CvtColor(mat, bgrMat, ColorConversionCodes.GRAY2BGR);
                return bgrMat;
            }
            else if (mat.Channels() == 4)
            {
                var bgrMat = new Mat();
                Cv2.CvtColor(mat, bgrMat, ColorConversionCodes.BGRA2BGR);
                return bgrMat;
            }
            else
            {
                throw new NotSupportedException($"不支持的通道数: {mat.Channels()}");
            }
        }

        /// <summary>
        /// 将Bitmap转换为字节数组
        /// </summary>
        private byte[] BitmapToByteArray(System.Drawing.Bitmap bitmap)
        {
            using var stream = new MemoryStream();
            bitmap.Save(stream, System.Drawing.Imaging.ImageFormat.Png);
            return stream.ToArray();
        }

        /// <summary>
        /// 记录Mat信息用于诊断
        /// </summary>
        private void LogMatInfo(string prefix, Mat mat)
        {
            if (mat == null)
            {
                Logger.Warn($"{prefix}: Mat为null");
                return;
            }

            Logger.Debug($"{prefix}: {mat.Width}x{mat.Height}, " +
                        $"通道: {mat.Channels()}, " +
                        $"深度: {mat.Depth()}, " +
                        $"类型: {mat.Type()}, " +
                        $"连续: {mat.IsContinuous()}, " +
                        $"总元素: {mat.Total()}, " +
                        $"元素大小: {mat.ElemSize()}");
        }

        /// <summary>
        /// 通过Bitmap转换（方法1）
        /// </summary>
        private SixLabors.ImageSharp.Image<Rgb24> ConvertViaBitmap(Mat mat)
        {
            // 确保是BGR格式，但不要重复转换
            Mat workingMat = mat;
            bool needsDispose = false;

            if (mat.Channels() == 1)
            {
                workingMat = new Mat();
                Cv2.CvtColor(mat, workingMat, ColorConversionCodes.GRAY2BGR);
                needsDispose = true;
            }
            else if (mat.Channels() == 4)
            {
                workingMat = new Mat();
                Cv2.CvtColor(mat, workingMat, ColorConversionCodes.BGRA2BGR);
                needsDispose = true;
            }
            // 如果已经是3通道，直接使用，不进行颜色空间转换

            try
            {
                // 直接转换为Bitmap（保持BGR格式）
                using var bitmap = OpenCvSharp.Extensions.BitmapConverter.ToBitmap(workingMat);

                // 从Bitmap创建ImageSharp图像，让ImageSharp自动处理颜色转换
                using var memoryStream = new MemoryStream();
                bitmap.Save(memoryStream, System.Drawing.Imaging.ImageFormat.Png); // 使用PNG保持质量
                memoryStream.Position = 0;

                var image = SixLabors.ImageSharp.Image.Load<Rgb24>(memoryStream);
                return image;
            }
            finally
            {
                if (needsDispose)
                    workingMat.Dispose();
            }
        }

        /// <summary>
        /// 优化的内存转换方法 - 最快速度
        /// </summary>
        private SixLabors.ImageSharp.Image<Rgb24> ConvertViaOptimizedMemory(Mat mat)
        {
            // 直接处理3通道BGR图像，减少不必要的转换
            if (mat.Channels() != 3)
            {
                // 只在必要时进行通道转换
                using var bgrMat = new Mat();
                if (mat.Channels() == 1)
                    Cv2.CvtColor(mat, bgrMat, ColorConversionCodes.GRAY2BGR);
                else if (mat.Channels() == 4)
                    Cv2.CvtColor(mat, bgrMat, ColorConversionCodes.BGRA2BGR);
                else
                    throw new NotSupportedException($"不支持的通道数: {mat.Channels()}");

                return ConvertViaOptimizedMemory(bgrMat);
            }

            var width = mat.Width;
            var height = mat.Height;
            var expectedSize = width * height * 3;

            // 直接从BGR转换为RGB，避免中间Mat创建
            var imageData = new byte[expectedSize];

            unsafe
            {
                var srcPtr = (byte*)mat.Data.ToPointer();
                fixed (byte* dstPtr = imageData)
                {
                    // 直接内存复制并同时进行BGR到RGB转换
                    for (int i = 0; i < expectedSize; i += 3)
                    {
                        dstPtr[i] = srcPtr[i + 2];     // R = B
                        dstPtr[i + 1] = srcPtr[i + 1]; // G = G
                        dstPtr[i + 2] = srcPtr[i];     // B = R
                    }
                }
            }

            return SixLabors.ImageSharp.Image.LoadPixelData<Rgb24>(imageData, width, height);
        }

        /// <summary>
        /// 通过内存直接转换（方法3）- 正确处理颜色空间
        /// </summary>
        private SixLabors.ImageSharp.Image<Rgb24> ConvertViaMemory(Mat mat)
        {
            Mat workingMat = mat;
            bool needsDispose = false;

            // 处理不同通道数的图像
            if (mat.Channels() == 1)
            {
                workingMat = new Mat();
                Cv2.CvtColor(mat, workingMat, ColorConversionCodes.GRAY2BGR);
                needsDispose = true;
            }
            else if (mat.Channels() == 4)
            {
                workingMat = new Mat();
                Cv2.CvtColor(mat, workingMat, ColorConversionCodes.BGRA2BGR);
                needsDispose = true;
            }

            try
            {
                // 重要：正确转换BGR到RGB
                using var rgbMat = new Mat();
                Cv2.CvtColor(workingMat, rgbMat, ColorConversionCodes.BGR2RGB);

                // 创建连续的内存布局
                var continuousMat = rgbMat.IsContinuous() ? rgbMat : rgbMat.Clone();

                try
                {
                    var width = continuousMat.Width;
                    var height = continuousMat.Height;
                    var channels = continuousMat.Channels();

                    if (_config.EnableDetailedTiming)
                    {
                        Logger.Debug($"RGB图像信息: {width}x{height}x{channels}, 连续: {continuousMat.IsContinuous()}");
                    }

                    // 确保是3通道RGB
                    if (channels != 3)
                    {
                        throw new InvalidOperationException($"期望3通道RGB图像，实际为{channels}通道");
                    }

                    // 使用Marshal.Copy进行安全的内存复制
                    var expectedSize = width * height * channels;
                    var imageData = new byte[expectedSize];

                    System.Runtime.InteropServices.Marshal.Copy(
                        continuousMat.Data, imageData, 0, expectedSize);

                    var image = SixLabors.ImageSharp.Image.LoadPixelData<Rgb24>(imageData, width, height);
                    return image;
                }
                finally
                {
                    if (continuousMat != rgbMat)
                        continuousMat.Dispose();
                }
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, $"内存转换失败: {ex.Message}");
                throw;
            }
            finally
            {
                if (needsDispose)
                    workingMat.Dispose();
            }
        }

        /// <summary>
        /// 处理检测结果 - 优化版本
        /// </summary>
        private void ProcessResults(Compunet.YoloSharp.Data.YoloResult<Compunet.YoloSharp.Data.Detection> yoloResult,
                                  Mat srcImage, InferenceResult result, string outputPrefix)
        {
            var processStopwatch = Stopwatch.StartNew();

            // 过滤检测结果计时
            var filterStopwatch = Stopwatch.StartNew();
            var validDetections = yoloResult
                .Where<Compunet.YoloSharp.Data.Detection>(d => d.Confidence >= _config.ConfidenceThreshold)
                .ToList();
            filterStopwatch.Stop();

            if (_config.EnableDetailedTiming)
            {
                Logger.Info($"检测结果过滤耗时：{filterStopwatch.ElapsedMilliseconds}ms，有效检测数：{validDetections.Count}");
            }

            // 优化：只在需要保存时创建目录
            var dirStopwatch = Stopwatch.StartNew();
            string outputDir = null;
            if (_config.SaveOriginImage || _config.SaveResultImage || _config.SaveCroppedDetections)
            {
                outputDir = "output/images";
                if (!Directory.Exists(outputDir))
                    Directory.CreateDirectory(outputDir);
            }
            dirStopwatch.Stop();

            if (_config.EnableDetailedTiming)
            {
                Logger.Info($"输出目录创建耗时：{dirStopwatch.ElapsedMilliseconds}ms");
            }

            // 优化：只在启用时保存原图
            var originSaveStopwatch = Stopwatch.StartNew();
            if (_config.SaveOriginImage && outputDir != null)
            {
                //// 创建原图副本避免修改原图
                using var originCopy = srcImage.Clone();
                Cv2.CvtColor(srcImage, originCopy, ColorConversionCodes.RGB2BGR);
                Cv2.ImWrite(Path.Combine(outputDir, $"{outputPrefix}_origin.jpg"), originCopy);
                
            }
            originSaveStopwatch.Stop();

            if (_config.EnableDetailedTiming)
            {
                Logger.Info($"原图保存耗时：{originSaveStopwatch.ElapsedMilliseconds}ms");
            }

            // 检测结果绘制计时
            var drawStopwatch = Stopwatch.StartNew();
            int croppedCount = 0;
            using var BeCropImage = srcImage.Clone();
            foreach (var detection in validDetections)
            {
                string className = detection.Name.Name.ToString().Length > 0 ? detection.Name.Name.ToString() : $"class_{detection.Name.Id}";

                // 绘制检测框
                var color = className.Contains("ok") ? Scalar.Red : Scalar.Blue;
                var rect = new Rect((int)detection.Bounds.X, (int)detection.Bounds.Y,
                                  (int)detection.Bounds.Width, (int)detection.Bounds.Height);

                int angle = 0;
                string displayClassName = className;



                // 优化：只在启用时保存裁剪图像,裁剪特征需要在绘制之前，避免被框影响。
                if (_config.SaveCroppedDetections && _config.CropLabels.Contains(className.ToLower()) && outputDir != null)
                {
                    // 对于需要角度检测的类别（COK2, FOK2），使用同步处理确保角度信息及时可用
                    if (className.ToLower() == "cok2" || className.ToLower() == "fok2")
                    {
                        var cropStopwatch = Stopwatch.StartNew();
                        try
                        {
                            var cropImageCopy = BeCropImage.Clone();
                            int ret = SaveCroppedDetection(cropImageCopy, className, detection.Confidence, rect, outputPrefix, result);
                            cropImageCopy?.Dispose();
                            cropStopwatch.Stop();
                            Logger.Info($"【耗时统计】同步角度检测和裁剪图像保存耗时：{cropStopwatch.ElapsedMilliseconds}ms - {className}");
                        }
                        catch (Exception ex)
                        {
                            cropStopwatch.Stop();
                            Logger.Error($"同步保存裁剪图像失败，耗时：{cropStopwatch.ElapsedMilliseconds}ms - {className} - {ex.Message}");
                        }
                    }
                    else
                    {
                        // 其他类别继续使用异步处理
                        var cropImageCopy = BeCropImage.Clone();
                        Task.Run(() =>
                        {
                            var cropStopwatch = Stopwatch.StartNew();
                            try
                            {
                                int ret = SaveCroppedDetection(cropImageCopy, className, detection.Confidence, rect, outputPrefix, result);
                                cropStopwatch.Stop();
                                Logger.Info($"【耗时统计】异步裁剪图像保存耗时：{cropStopwatch.ElapsedMilliseconds}ms - {className}");
                            }
                            catch (Exception ex)
                            {
                                cropStopwatch.Stop();
                                Logger.Error($"异步保存裁剪图像失败，耗时：{cropStopwatch.ElapsedMilliseconds}ms - {className} - {ex.Message}");
                            }
                            finally
                            {
                                cropImageCopy?.Dispose();
                            }
                        });
                    }

                    croppedCount++;
                }

                // 如果是cok2类别且获取到有效角度值，添加到PinInfos并更新显示名称
                if (className.ToLower() == "cok2" && angle > 0)
                {
                    //result.PinInfos.Add(new PinInfo { Angle = angle, Height = 0 });
                    displayClassName = $"{className}({angle})";
                }

                Cv2.PutText(srcImage, $"{displayClassName}:{detection.Confidence:P0}",
                           rect.TopLeft, HersheyFonts.HersheyPlain, 10, color);
                Cv2.Rectangle(srcImage, rect, color, thickness: 3);

                result.Detections.Add(new KeyValuePair<string, float>(className.ToLower(), detection.Confidence));
            }
            drawStopwatch.Stop();

            BeCropImage.Release();

            if (_config.EnableDetailedTiming)
            {
                Logger.Info($"检测框绘制耗时：{drawStopwatch.ElapsedMilliseconds}ms，裁剪保存数量：{croppedCount}");
            }

            // 优化：只在启用时保存结果图像 - 异步保存
            var resultSaveStopwatch = Stopwatch.StartNew();
            if (_config.SaveResultImage && outputDir != null)
            {
                result.ResultImagePath = Path.Combine(outputDir, $"{outputPrefix}_result.jpg");
                // 创建副本用于保存，避免原图被改动
                using var resultCopy = srcImage.Clone();

                // RGB 转 BGR（用于 JPEG 保存）
                Cv2.CvtColor(resultCopy, resultCopy, ColorConversionCodes.RGB2BGR);
                result.ProcessedImage = resultCopy.Clone();

                // 异步保存结果图像，不阻塞主流程
                Task.Run(() =>
                {
                    var saveStopwatch = Stopwatch.StartNew();
                    try
                    {
                        using var resultCopy1 = srcImage.Clone();
                        Cv2.CvtColor(resultCopy1, resultCopy1, ColorConversionCodes.RGB2BGR);

                        // 在子线程中保存结果图像
                        Cv2.ImWrite(result.ResultImagePath, resultCopy1);
                        saveStopwatch.Stop();

                        Logger.Info($"【耗时统计】异步保存结果图像耗时：{saveStopwatch.ElapsedMilliseconds}ms - {result.ResultImagePath}");
                    }
                    catch (Exception ex)
                    {
                        saveStopwatch.Stop();
                        Logger.Error($"异步保存结果图像失败，耗时：{saveStopwatch.ElapsedMilliseconds}ms - {ex.Message}");
                    }
                });
            }
            resultSaveStopwatch.Stop();

            if (_config.EnableDetailedTiming)
            {
                Logger.Info($"结果图像保存耗时：{resultSaveStopwatch.ElapsedMilliseconds}ms");
            }

            // 图像克隆计时
            //var cloneStopwatch = Stopwatch.StartNew();
            ////result.ProcessedImage = resultCopy; // srcImage.Clone();
            //cloneStopwatch.Stop();

            //if (_config.EnableDetailedTiming)
            //{
            //    Logger.Info($"图像克隆耗时：{cloneStopwatch.ElapsedMilliseconds}ms");
            //}

            processStopwatch.Stop();

            // 优化：只在启用详细计时时输出详细报告
            if (_config.EnableDetailedTiming)
            {
                string processTimingReport = $"=== ProcessResults耗时统计 ===\n" +
                                           $"检测结果过滤：{filterStopwatch.ElapsedMilliseconds}ms\n" +
                                           $"输出目录创建：{dirStopwatch.ElapsedMilliseconds}ms\n" +
                                           $"原图保存：{originSaveStopwatch.ElapsedMilliseconds}ms\n" +
                                           $"检测框绘制：{drawStopwatch.ElapsedMilliseconds}ms\n" +
                                           $"结果图像保存：{resultSaveStopwatch.ElapsedMilliseconds}ms\n" +
                                           $"ProcessResults总耗时：{processStopwatch.ElapsedMilliseconds}ms\n" +
                                           $"============================";

                Logger.Info(processTimingReport);
                Console.WriteLine(processTimingReport);
            }
            else
            {
                // 简化日志输出
                Logger.Info($"结果处理耗时：{processStopwatch.ElapsedMilliseconds}ms，检测数量：{validDetections.Count}");
            }
        }



        /// <summary>
        /// 保存裁剪的检测区域
        /// </summary>
        private int SaveCroppedDetection(Mat srcImage, string className, float confidence, Rect rect, string outputPrefix, InferenceResult result)
        {
            var cropStopwatch = Stopwatch.StartNew();

            try
            {
                // 区域校验计时
                var validateStopwatch = Stopwatch.StartNew();
                rect.X = Math.Max(0, rect.X);
                rect.Y = Math.Max(0, rect.Y - 20);
                rect.Width = Math.Min(rect.Width, srcImage.Width - rect.X);
                rect.Height = Math.Min(rect.Height, srcImage.Height - rect.Y) + 15;

                if (rect.Width <= 0 || rect.Height <= 0)
                {
                    validateStopwatch.Stop();
                    Logger.Info($"裁剪区域无效，跳过保存 - 区域校验耗时：{validateStopwatch.ElapsedMilliseconds}ms");
                    return -1;
                }
                validateStopwatch.Stop();

                // 目录创建计时
                var dirStopwatch = Stopwatch.StartNew();
                var cropDir = Path.Combine("output", $"{className.ToLower()}_save");
                if (!Directory.Exists(cropDir))
                    Directory.CreateDirectory(cropDir);
                dirStopwatch.Stop();

                // 图像裁剪计时
                var imageCropStopwatch = Stopwatch.StartNew();
                using var croppedImage = new Mat(srcImage, rect);
                using var rgbImage = new Mat();
                Cv2.CvtColor(croppedImage, rgbImage, ColorConversionCodes.BGR2RGB);
                imageCropStopwatch.Stop();

                // 文件名生成计时
                var nameStopwatch = Stopwatch.StartNew();
                var timestamp = DateTime.Now.ToString("yyyyMMdd_HHmmss_fff");
                var fileName = $"{outputPrefix}_{className}_{confidence:F2}_{timestamp}.jpg";
                var filePath = Path.Combine(cropDir, fileName);
                nameStopwatch.Stop();

                // 文件保存计时
                var saveStopwatch = Stopwatch.StartNew();
                using var bgrForSave = new Mat();
                Cv2.CvtColor(rgbImage, bgrForSave, ColorConversionCodes.RGB2BGR);
                Cv2.ImWrite(filePath, bgrForSave);
                saveStopwatch.Stop();
                
                cropStopwatch.Stop();
                var angles = new List<PinInfo>();
                Logger.Info($"Corpped path :{filePath}");
                if (className.ToLower() == "cok2")
                {
                    angles = YoloMaskProcessor.GetPinAngles(filePath);
                }
                    
                if (className.ToLower() == "fok2")
                {
                    angles = ProcessFok2Detection(filePath, timestamp);
                    if (angles == null)
                    {
                        Logger.Warn($"FOK2检测返回null角度信息：{filePath}");
                        return -1; // 返回-1表示没有有效角度
                    }
                    Logger.Info($"FOK2检测成功，获得{angles.Count}个角度信息：{string.Join(", ", angles.Select(a => a.Angle.ToString("F1")))}");
                }




                if (angles == null || angles.Count == 0)
                {
                    Logger.Warn($"未检测到有效角度，跳过保存裁剪图像：{filePath}");
                    return -1; // 返回-1表示没有有效角度
                }
                

                //var results = YoloMaskProcessor.ProcessImageAndGetAngleAsync(filePath);
                //results.Wait(); // 等待异步处理完成
                ////results.Result
                //List<PinInfo> pinInfos = new List<PinInfo>();
                
                //for (int i = 0; i < results.Result.Count; i++)
                //{
                //    var result1 = results.Result[i];
                //    if (result1 == null)
                //    {
                //        Logger.Warn($"处理结果为空，跳过保存裁剪图像：{filePath}");
                //        continue;
                //    }
                //    var angle = result1.Angle;
                //    var fileName1 = $"{outputPrefix}_{className}_{confidence:F2}_{timestamp}_angle.jpg";
                //    var filePath1 = Path.Combine(cropDir, fileName1);


                //    Cv2.CvtColor(result1.ProcessedImage, result1.ProcessedImage, ColorConversionCodes.RGB2BGR);
                //    var save_ret = Cv2.ImWrite(filePath1, result1.ProcessedImage);
                //    Logger.Info($"处理结果：cols:{result1.ProcessedImage.Cols}，rows:{result1.ProcessedImage.Rows},angle:{angle}，area:{result1.ProcessedImage.ContourArea}");
                //    PinInfo pinInfo = new PinInfo();

                //    pinInfo.Angle = (float)angle;
                //    pinInfos.Add(pinInfo);
                //    //if (angle > 9)
                //    //{
                //        //return (int)angle;
                //    //}
                //}
                result.PinInfos = angles;
                Logger.Info($"设置角度信息到result.PinInfos：{angles?.Count ?? 0}个角度，值：{string.Join(", ", angles?.Select(a => a.Angle.ToString("F1")) ?? new string[0])}");
                Logger.Info($"裁剪图像保存完成：{filePath} - " +
                          $"区域校验：{validateStopwatch.ElapsedMilliseconds}ms，" +
                          $"目录创建：{dirStopwatch.ElapsedMilliseconds}ms，" +
                          $"图像裁剪：{imageCropStopwatch.ElapsedMilliseconds}ms，" +
                          $"文件名生成：{nameStopwatch.ElapsedMilliseconds}ms，" +
                          $"文件保存：{saveStopwatch.ElapsedMilliseconds}ms，" +
                          $"总耗时：{cropStopwatch.ElapsedMilliseconds}ms");
                return 0; // 返回0表示成功
            }
            catch (Exception ex)
            {
                cropStopwatch.Stop();
                Logger.Exception(ex, $"保存裁剪图像失败：{className}，耗时：{cropStopwatch.ElapsedMilliseconds}ms");
                return -1; // 返回-1表示失败
            }
        }

        /// <summary>
        /// 处理FOK2检测逻辑
        /// </summary>
        /// <param name="filePath">图像文件路径</param>
        /// <param name="timestamp">时间戳</param>
        /// <returns>检测到的角度信息列表，失败时返回null</returns>
        private List<PinInfo>? ProcessFok2Detection(string filePath, string timestamp)
        {
            if (_fokPredictor == null)
            {
                Logger.Error("FOK2预测器未初始化");
                return null;
            }

            try
            {
                // 配置FOK2检测参数，与demo保持一致
                var config = YoloConfiguration.Default;
                config.Confidence = 0.3f;  // 使用与应用配置一致的阈值
                config.IoU = 0.6f;         // 设置IoU阈值，与COK2保持一致

                Logger.Info($"FOK2检测配置 - Confidence: {config.Confidence}, IoU: {config.IoU}");

                var yoloResult = _fokPredictor.DetectAndSave(filePath, YoloMaskProcessor.AddSuffix(filePath, "_angle"));
                var validDetections = yoloResult
                    .Where(d => d.Confidence >= config.Confidence)  // 使用配置的阈值
                    .ToList();

                Logger.Info($"FOK2检测结果 - 总检测数: {yoloResult.Count()}, 有效检测数: {validDetections.Count}");

                if (!IsValidFok2Detection(validDetections, filePath))
                {
                    return null;
                }

                var (angle,distance) = CalculateAngleFromDetections(validDetections);
                var pinInfo = new PinInfo(angle,distance);

                LogFok2Results(timestamp, angle, validDetections);

                return new List<PinInfo> { pinInfo };
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, $"FOK2检测处理失败：{filePath}");
                return null;
            }
        }

        /// <summary>
        /// 验证FOK2检测结果是否有效
        /// </summary>
        private bool IsValidFok2Detection(List<Detection> detections, string filePath)
        {
            if (detections.Count != REQUIRED_FOK2_DETECTIONS)
            {
                Logger.Warn($"FOK2检测未找到有效目标，跳过保存裁剪图像：{filePath}，Count：{detections.Count}");
                return false;
            }
            return true;
        }

        /// <summary>
        /// 从检测结果计算角度
        /// </summary>
        private (double,double) CalculateAngleFromDetections(List<Detection> validDetections)
        {
            var rectangle1 = validDetections[0].Bounds;
            var rectangle2 = validDetections[1].Bounds;

            var angle = Math.Abs(GetHorizontalAngle(rectangle1, rectangle2));
            var distance = GetCenterDistance(rectangle1, rectangle2);
            Logger.Info($"Distance:{distance}");
            // 角度标准化：确保角度在0-90度范围内
            return (angle > ANGLE_THRESHOLD ? 180 - angle : angle, distance);
        }

        public static double GetCenterDistance(SixLabors.ImageSharp.Rectangle rect1, SixLabors.ImageSharp.Rectangle rect2)
        {
            // 求中心点
            double cx1 = rect1.X + rect1.Width / 2.0;
            double cy1 = rect1.Y + rect1.Height / 2.0;

            double cx2 = rect2.X + rect2.Width / 2.0;
            double cy2 = rect2.Y + rect2.Height / 2.0;

            // 计算欧几里得距离
            double dx = cx2 - cx1;
            double dy = cy2 - cy1;
            double distance = Math.Sqrt(dx * dx + dy * dy);

            return distance;
        }

        /// <summary>
        /// 记录FOK2检测结果日志
        /// </summary>
        private void LogFok2Results(string timestamp, double angle, List<Detection> validDetections)
        {
            Logger.Info($"{timestamp} FOK2检测到角度：{angle:F2}°");

            foreach (var detection in validDetections)
            {
                Logger.Info($"FOK2检测到：{detection.Name.Name}，置信度：{detection.Confidence:F2}, 位置：({detection.Bounds.X}, {detection.Bounds.Y}, {detection.Bounds.Width}, {detection.Bounds.Height})");
            }
        }

        /// <summary>
        /// 诊断FOK2检测配置，用于对比demo差异
        /// </summary>
        public void DiagnoseFok2Configuration()
        {
            Logger.Info("=== FOK2检测配置诊断 ===");
            Logger.Info($"模型路径: {_config.FOKModelPath}");
            Logger.Info($"置信度阈值: {_config.ConfidenceThreshold}");
            Logger.Info($"模型是否存在: {File.Exists(_config.FOKModelPath)}");
            Logger.Info($"FOK预测器是否初始化: {_fokPredictor != null}");

            var defaultConfig = YoloConfiguration.Default;
            Logger.Info($"默认YoloConfiguration - Confidence: {defaultConfig.Confidence}, IoU: {defaultConfig.IoU}");
            Logger.Info("=== 诊断结束 ===");
        }

        public static double GetHorizontalAngle(SixLabors.ImageSharp.Rectangle rect1, SixLabors.ImageSharp.Rectangle rect2)
        {
            // 求中心点
            double cx1 = rect1.X + rect1.Width / 2.0;
            double cy1 = rect1.Y + rect1.Height / 2.0;

            double cx2 = rect2.X + rect2.Width / 2.0;
            double cy2 = rect2.Y + rect2.Height / 2.0;

            double dx = cx2 - cx1;
            double dy = cy2 - cy1;

            double angleRadians = Math.Atan2(dy, dx);
            double angleDegrees = angleRadians * (180.0 / Math.PI);

            return angleDegrees;
        }


        /// <summary>
        /// 获取推理统计信息
        /// </summary>
        public string GetInferenceStats(InferenceResult result)
        {
            return $"推理: {result.InferenceTime:F2}ms, 检测数量: {result.Detections.Count}";
        }

        /// <summary>
        /// 检查模型是否已初始化
        /// </summary>
        public bool IsInitialized => _isInitialized;

        /// <summary>
        /// 获取类别标签
        /// </summary>
        public string[] ClassLabels => _classLabels;

        /// <summary>
        /// 启用性能优化模式（推荐用于生产环境）
        /// </summary>
        public void EnablePerformanceMode()
        {
            _config.EnableDetailedTiming = false;
            _config.OptimizeImageConversion = true;
            _config.SkipUnnecessaryOperations = true;
            _config.SaveOriginImage = false;
            _config.SaveResultImage = false;
            _config.SaveCroppedDetections = false;
            Logger.Info("已启用性能优化模式");
        }

        /// <summary>
        /// 启用调试模式（用于性能分析和问题诊断）
        /// </summary>
        public void EnableDebugMode()
        {
            _config.EnableDetailedTiming = true;
            _config.OptimizeImageConversion = false;
            _config.SkipUnnecessaryOperations = false;
            Logger.Info("已启用调试模式");
        }

        /// <summary>
        /// 获取当前配置信息
        /// </summary>
        public string GetConfigInfo()
        {
            return $"配置信息：优化转换={_config.OptimizeImageConversion}, " +
                   $"详细计时={_config.EnableDetailedTiming}, " +
                   $"跳过不必要操作={_config.SkipUnnecessaryOperations}, " +
                   $"保存原图={_config.SaveOriginImage}, " +
                   $"保存结果图={_config.SaveResultImage}, " +
                   $"保存裁剪图={_config.SaveCroppedDetections}";
        }

        public void Dispose()
        {
            try
            {
                _predictor?.Dispose();
                _pinPredictor?.Dispose();
                _fokPredictor?.Dispose();
                _isInitialized = false;
                Logger.Info("YoloSharp已释放");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "释放YoloSharp异常");
            }
        }
    }
}
