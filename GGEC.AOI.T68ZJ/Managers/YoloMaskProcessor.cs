using Compunet.YoloSharp;              // 核心包
using Compunet.YoloSharp.Plotting;
using GGEC.AOI.T68ZJ.Client.Helper;
using GGEC.AOI.T68ZJ.Log;
using GGEC.AOI.T68ZJ.Managers;
using Microsoft.VisualBasic.ApplicationServices;
using OpenCvSharp;
using SixLabors.ImageSharp;            // 图像载入
using System.Reflection;
using Point = OpenCvSharp.Point;


/// <summary>
/// 角度检测结果
/// </summary>
public class AngleDetectionResult
{
    /// <summary>
    /// 检测到的角度（度）
    /// </summary>
    public double Angle { get; set; }
    /// <summary>
    /// 是否成功检测到角度
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 错误信息（如果检测失败）
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 处理后的图像（可选，用于调试）
    /// </summary>
    public Mat? ProcessedImage { get; set; }

    /// <summary>
    /// 检测到的对象名称
    /// </summary>
    public string? ObjectName { get; set; }

    /// <summary>
    /// 检测置信度
    /// </summary>
    public float Confidence { get; set; }
}

public static class YoloMaskProcessor
{
    private static YoloPredictor? _predictor;
    private static double _areaThreshold;

    /// <summary>
    /// 初始化分割预测器
    /// </summary>
    /// <param name="modelPath">ONNX 模型文件路径</param>
    /// <param name="areaThreshold">过滤小轮廓的面积阈值</param>
    public static void Initialize(string modelPath, double areaThreshold = 100)
    {
        // 构造时无需特别指定 TaskType，库会根据模型自动识别
        _predictor = new YoloPredictor(modelPath);
        _areaThreshold = areaThreshold;
    }

    public static List<PinInfo>? GetPinAngles(string imgPath)
    {
        if (_predictor == null)
        {
            return null;
        }
        var Config = YoloConfiguration.Default;
        Config.Confidence = 0.79f;
        Config.IoU = 0.6f;
        var results = _predictor.DetectObbAndSave(imgPath, AddSuffix(imgPath,"_angle"));
        var Preprocess = results.Speed.Preprocess;
        var Inference = results.Speed.Inference;
        var Postprocess = results.Speed.Postprocess;
        Logger.Info($"Get Pin Angle Speed(Preprocess:{Preprocess.TotalMilliseconds:F2}ms Inference:{Inference.TotalMilliseconds:F2}ms Postprocess:{Postprocess.TotalMilliseconds:F2}ms)");
        List<PinInfo> angles = new List<PinInfo>();
        foreach (var result in results)
        {
            PinInfo pinInfo = new PinInfo();
            if (result.Angle >50)
            {
                pinInfo.Angle = 90 - result.Angle;
            } else
            {
                pinInfo.Angle = result.Angle;
            }
                
            Logger.Info($"Angle:{result.Angle} Confidence:{result.Confidence}");
            angles.Add(pinInfo);
        }
        return angles;
    }
    public static string AddSuffix(string path, string suffix = "_result", bool processDirectory = false)
    {
        if (string.IsNullOrEmpty(path))
            return path;

        try
        {
            if (processDirectory && Directory.Exists(path))
            {
                // 处理目录（在目录名后添加后缀）
                string dirName = Path.GetFileName(path);
                string parentDir = Path.GetDirectoryName(path) ?? "";
                return Path.Combine(parentDir, $"{dirName}{suffix}");
            }
            else
            {
                // 处理文件
                string fileNameWithoutExt = Path.GetFileNameWithoutExtension(path);
                string extension = Path.GetExtension(path);
                string directory = Path.GetDirectoryName(path) ?? "";
                return Path.Combine(directory, $"{fileNameWithoutExt}{suffix}{extension}");
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"警告：处理路径时出错 - {ex.Message}");
            return path;
        }
    }

    /// <summary>
    /// 对单张图片做实例分割、直线拟合并绘制
    /// </summary>
    /// <param name="imgPath">图片文件路径</param>
    /// <returns>绘制好结果的 OpenCvSharp Mat，如果没有检测到结果则返回null</returns>
    public static async Task<Mat?> ProcessImageAsync(string imgPath)
    {
        var results = await ProcessImageAndGetAngleAsync(imgPath);

        // 返回第一个成功处理的图像，如果有的话
        var successfulResult = results.FirstOrDefault(r => r.IsSuccess && r.ProcessedImage != null);
        return successfulResult?.ProcessedImage;
    }

    /// <summary>
    /// 对单张图片做实例分割并计算角度（封装版本）
    /// </summary>
    /// <param name="imgPath">图片文件路径</param>
    /// <returns>包含角度信息的结果列表</returns>
    public static async Task<List<AngleDetectionResult>> ProcessImageAndGetAngleAsync(string imgPath)
    {
        if (_predictor == null)
        {
            return new List<AngleDetectionResult>
            {
                new()
                {
                    IsSuccess = false,
                    ErrorMessage = "Predictor not initialized. Call Initialize() first."
                }
            };
        }

        try
        {
            // 1. 用 ImageSharp 载入，用于推理
            //using var imgSharp = Image.Load<Rgb24>(imgPath);

            // 2. 异步检测推理（使用分割）
            var detectResult = await _predictor.SegmentAsync(imgPath);

            // 3. OpenCvSharp 读取同一张图用于绘制
            Mat img = Cv2.ImRead(imgPath);

            // 4. 探索检测结果的结构
            Console.WriteLine($"Detection result type: {detectResult.GetType()}");
            Console.WriteLine($"Detection result count: {detectResult.Count()}");


            // 4.1. 当检测结果数量大于2时，移除面积最小的一个
            var detectionList = detectResult.ToList();
            if (detectionList.Count > 2)
            {
                // 计算每个检测结果的面积并找到最小的
                var detectionWithArea = detectionList
                    .Select(d => new { Detection = d, Area = d.Bounds.Width * d.Bounds.Height })
                    .OrderBy(x => x.Area)
                    .ToList();

                var smallestDetection = detectionWithArea.First();
                Console.WriteLine($"Removing smallest detection with area: {smallestDetection.Area} (bounds: {smallestDetection.Detection.Bounds.Width}x{smallestDetection.Detection.Bounds.Height})");

                // 移除面积最小的检测结果
                detectionList.Remove(smallestDetection.Detection);
                Console.WriteLine($"Detection count after removal: {detectionList.Count}");
            }

            // 5. 遍历每个检测结果，处理所有有效的角度
            int resultIndex = 0;
            List<AngleDetectionResult> angleDetectionResults = new List<AngleDetectionResult>();

            foreach (var detectionResult in detectionList)
            {
                Console.WriteLine($"Processing result {resultIndex + 1}: {detectionResult.Name} (confidence: {detectionResult.Confidence:F2})");

                // 绘制边界框
                var bounds = detectionResult.Bounds;

                // 添加标签
                string label = $"{detectionResult.Name}: {detectionResult.Confidence:F2}";
                Cv2.PutText(img, label,
                    new OpenCvSharp.Point(bounds.X, bounds.Y - 10),
                    HersheyFonts.HersheySimplex, 0.5, new Scalar(0, 255, 0), 1);

                // 处理分割掩码
                if (detectionResult.Mask != null)
                {
                    Console.WriteLine($"Mask size: {detectionResult.Mask.Width}x{detectionResult.Mask.Height}");

                    // 方法1：绘制半透明填充掩码（推荐用于可视化）
                    DrawMaskOnImage(img, detectionResult.Mask, bounds);

                    // 方法2：只绘制掩码轮廓（推荐用于精确边界显示）
                    // DrawMaskContour(img, detectionResult.Mask, bounds, new Scalar(255, 0, 0), 3); // 蓝色轮廓，3像素粗

                    // 自定义颜色示例：
                    // DrawMaskOnImage(img, detectionResult.Mask, bounds, new Scalar(0, 0, 255), 0.3); // 半透明红色
                    // DrawMaskOnImage(img, detectionResult.Mask, bounds, new Scalar(255, 255, 0), 0.6); // 半透明青色

                    // 首先尝试查找是否有直接的轮廓坐标访问方法
                    ExploreSegmentationResult(detectionResult);

                    // 提取轮廓并进行直线拟合，获取角度
                    var angle = ProcessMaskContoursForAngle(img, detectionResult.Mask, bounds);
                    Console.WriteLine($"================================Detected angle: {angle}");

                    // 创建角度检测结果
                    var angleResult = new AngleDetectionResult
                    {
                        IsSuccess = angle.HasValue,
                        Angle = angle ?? 0.0,
                        ProcessedImage = img.Clone(), // 为每个结果创建独立的图像副本
                        ObjectName = detectionResult.Name.ToString(),
                        Confidence = detectionResult.Confidence,
                        ErrorMessage = angle.HasValue ? null : "Failed to detect angle from mask contours"
                    };

                    angleDetectionResults.Add(angleResult);
                }
                else
                {
                    // 即使没有掩码，也添加一个失败的结果记录
                    var angleResult = new AngleDetectionResult
                    {
                        IsSuccess = false,
                        Angle = 0.0,
                        ProcessedImage = null,
                        ObjectName = detectionResult.Name.ToString(),
                        Confidence = detectionResult.Confidence,
                        ErrorMessage = "No mask data available for angle detection"
                    };

                    angleDetectionResults.Add(angleResult);
                }

                resultIndex++;
            }

            // 如果没有检测到任何结果，返回一个空列表而不是错误
            if (angleDetectionResults.Count == 0)
            {
                Console.WriteLine("No objects detected in the image");
            }

            // 释放原始图像资源
            img?.Dispose();

            return angleDetectionResults;
        }
        catch (Exception ex)
        {
            return new List<AngleDetectionResult>
            {
                new()
                {
                    IsSuccess = false,
                    ErrorMessage = $"Error processing image: {ex.Message}"
                }
            };
        }
    }

    /// <summary>
    /// 简化的API：只返回第一个成功检测到的角度
    /// </summary>
    /// <param name="imgPath">图片文件路径</param>
    /// <returns>检测到的角度（度），如果检测失败返回null</returns>
    public static async Task<double?> GetAngleAsync(string imgPath)
    {
        var results = await ProcessImageAndGetAngleAsync(imgPath);
        var successfulResult = results.FirstOrDefault(r => r.IsSuccess);
        return successfulResult?.Angle;
    }

    /// <summary>
    /// 获取所有检测到的角度
    /// </summary>
    /// <param name="imgPath">图片文件路径</param>
    /// <returns>所有成功检测到的角度列表</returns>
    public static async Task<List<double>> GetAllAnglesAsync(string imgPath)
    {
        var results = await ProcessImageAndGetAngleAsync(imgPath);
        return results.Where(r => r.IsSuccess).Select(r => r.Angle).ToList();
    }

    /// <summary>
    /// 获取检测结果摘要
    /// </summary>
    /// <param name="imgPath">图片文件路径</param>
    /// <returns>检测结果摘要信息</returns>
    public static async Task<(int totalDetections, int successfulDetections, List<string> objectNames)> GetDetectionSummaryAsync(string imgPath)
    {
        var results = await ProcessImageAndGetAngleAsync(imgPath);
        var successfulResults = results.Where(r => r.IsSuccess).ToList();
        var objectNames = results.Where(r => !string.IsNullOrEmpty(r.ObjectName))
                                .Select(r => r.ObjectName!)
                                .ToList();

        return (results.Count, successfulResults.Count, objectNames);
    }

    /// <summary>
    /// 将掩码绘制到原图上
    /// </summary>
    /// <param name="img">原图</param>
    /// <param name="mask">分割掩码</param>
    /// <param name="bounds">边界框</param>
    /// <param name="maskColor">掩码颜色，默认为半透明绿色</param>
    /// <param name="alpha">透明度，0.0-1.0，默认0.5</param>
    private static void DrawMaskOnImage(Mat img, Compunet.YoloSharp.Memory.BitmapBuffer mask, SixLabors.ImageSharp.Rectangle bounds, Scalar? maskColor = null, double alpha = 0.5)
    {
        try
        {
            // 默认使用半透明绿色
            Scalar color = maskColor ?? new Scalar(0, 255, 0); // BGR格式：绿色

            // 将 YoloSharp 的 BitmapBuffer 转换为 OpenCV Mat
            Mat? maskMat = ConvertBitmapBufferToMat(mask);

            if (maskMat == null || maskMat.Empty())
            {
                Console.WriteLine("Failed to convert mask to OpenCV Mat for drawing");
                return;
            }

            // 创建一个与原图大小相同的掩码
            Mat fullSizeMask = Mat.Zeros(img.Height, img.Width, MatType.CV_8UC1);

            // 将掩码缩放并放置到正确的位置
            Mat resizedMask = new Mat();
            Cv2.Resize(maskMat, resizedMask, new OpenCvSharp.Size(bounds.Width, bounds.Height));

            // 确保边界框在图像范围内
            int x = Math.Max(0, bounds.X);
            int y = Math.Max(0, bounds.Y);
            int width = Math.Min(bounds.Width, img.Width - x);
            int height = Math.Min(bounds.Height, img.Height - y);

            if (width > 0 && height > 0)
            {
                // 将调整大小的掩码复制到全尺寸掩码的正确位置
                Rect roi = new Rect(x, y, width, height);
                Mat roiMask = new Mat(fullSizeMask, roi);

                // 如果调整大小的掩码尺寸与ROI不匹配，再次调整
                if (resizedMask.Width != width || resizedMask.Height != height)
                {
                    Mat finalResizedMask = new Mat();
                    Cv2.Resize(resizedMask, finalResizedMask, new OpenCvSharp.Size(width, height));
                    finalResizedMask.CopyTo(roiMask);
                    finalResizedMask.Dispose();
                }
                else
                {
                    resizedMask.CopyTo(roiMask);
                }

                roiMask.Dispose();
            }

            // 创建彩色掩码
            Mat colorMask = Mat.Zeros(img.Height, img.Width, MatType.CV_8UC3);
            colorMask.SetTo(color, fullSizeMask);

            // 将彩色掩码叠加到原图上
            Cv2.AddWeighted(img, 1.0 - alpha, colorMask, alpha, 0, img);

            // 清理资源
            maskMat?.Dispose();
            fullSizeMask?.Dispose();
            resizedMask?.Dispose();
            colorMask?.Dispose();

            Console.WriteLine($"Successfully drew mask on image with alpha={alpha:F2}");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error drawing mask on image: {ex.Message}");
        }
    }

    /// <summary>
    /// 处理分割掩码，提取轮廓并进行直线拟合，只返回角度
    /// </summary>
    private static double? ProcessMaskContoursForAngle(Mat img, Compunet.YoloSharp.Memory.BitmapBuffer mask, SixLabors.ImageSharp.Rectangle bounds)
    {
        try
        {
            // 将 YoloSharp 的 BitmapBuffer 转换为 OpenCV Mat
            Mat? maskMat = ConvertBitmapBufferToMat(mask);

            if (maskMat == null || maskMat.Empty())
            {
                Console.WriteLine("Failed to convert mask to OpenCV Mat");
                return null;
            }

            // 应用形态学操作来改善掩码质量
            Mat processedMask = PreprocessMask(maskMat);

            // 在处理后的掩码上查找轮廓
            Cv2.FindContours(processedMask, out Point[][] contours, out HierarchyIndex[] hierarchy, RetrievalModes.External, ContourApproximationModes.ApproxSimple);

            // 清理处理后的掩码
            processedMask?.Dispose();

            Console.WriteLine($"Found {contours.Length} contours in mask {maskMat.Width}x{maskMat.Height}");

            if (contours.Length == 0)
            {
                Console.WriteLine("No contours found after preprocessing");
                maskMat?.Dispose();
                return null;
            }

            // 计算从掩码坐标到原图坐标的映射关系
            double scaleX = (double)bounds.Width / maskMat.Width;
            double scaleY = (double)bounds.Height / maskMat.Height;
            int offsetX = bounds.X;
            int offsetY = bounds.Y;

            // 按面积排序，选择最大的轮廓前2个
            var sortedContours = contours
                .Select((contour, index) => new { Contour = contour, Area = Cv2.ContourArea(contour), Index = index })
                .Where(x => x.Area >= 50) // 降低阈值，因为现在是在掩码坐标系中
                .OrderByDescending(x => x.Area).Take(2)
                .ToList();

            if (sortedContours.Count == 0)
            {
                Console.WriteLine("No contours meet the area threshold");
                maskMat?.Dispose();
                return null;
            }

            // 处理最大的轮廓
            var largestContourInfo = sortedContours.First();
            var contour = largestContourInfo.Contour;
            Console.WriteLine($"Processing largest contour with area: {largestContourInfo.Area:F2}, points: {contour.Length}");

            // 将轮廓坐标映射到原图坐标系
            Point[] mappedContour = new Point[contour.Length];
            for (int j = 0; j < contour.Length; j++)
            {
                mappedContour[j] = new Point(
                    (int)(contour[j].X * scaleX + offsetX),
                    (int)(contour[j].Y * scaleY + offsetY)
                );
            }
            // 拟合直线并获取角度（使用映射后的坐标）
            //var angle = FitLineAndGetAngle(mappedContour);
            var ( p1, p2, angle) = FitAndDrawLine(img, mappedContour);
            // 清理资源
            maskMat?.Dispose();

            return angle;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error processing mask contours for angle: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 探索分割结果的结构，寻找轮廓坐标
    /// </summary>
    private static void ExploreSegmentationResult(dynamic result)
    {
        try
        {
            Console.WriteLine($"Exploring segmentation result structure...");
            var resultType = result.GetType();
            Console.WriteLine($"Result type: {resultType}");

            // 查看所有属性
            var properties = resultType.GetProperties();
            Console.WriteLine("Available properties:");
            foreach (var prop in properties)
            {
                try
                {
                    var value = prop.GetValue(result);
                    Console.WriteLine($"  {prop.Name}: {value?.GetType()} = {value}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"  {prop.Name}: Error - {ex.Message}");
                }
            }

            // 查看所有字段
            var fields = resultType.GetFields(BindingFlags.Public | BindingFlags.NonPublic | BindingFlags.Instance);
            Console.WriteLine("Available fields:");
            foreach (var field in fields)
            {
                try
                {
                    var value = field.GetValue(result);
                    Console.WriteLine($"  {field.Name}: {value?.GetType()} = {value}");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"  {field.Name}: Error - {ex.Message}");
                }
            }
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error exploring segmentation result: {ex.Message}");
        }
    }

    /// <summary>
    /// 处理分割掩码，提取轮廓并进行直线拟合
    /// </summary>
    private static void ProcessMaskContours(Mat img, Compunet.YoloSharp.Memory.BitmapBuffer mask, SixLabors.ImageSharp.Rectangle bounds)
    {
        try
        {
            // 将 YoloSharp 的 BitmapBuffer 转换为 OpenCV Mat
            Mat? maskMat = ConvertBitmapBufferToMat(mask);

            if (maskMat == null || maskMat.Empty())
            {
                Console.WriteLine("Failed to convert mask to OpenCV Mat");
                return;
            }

            // 应用形态学操作来改善掩码质量
            Mat processedMask = PreprocessMask(maskMat);

            // 在处理后的掩码上查找轮廓
            Cv2.FindContours(processedMask, out Point[][] contours, out HierarchyIndex[] hierarchy, RetrievalModes.External, ContourApproximationModes.ApproxSimple);

            // 清理处理后的掩码
            processedMask?.Dispose();

            Console.WriteLine($"Found {contours.Length} contours in mask {maskMat.Width}x{maskMat.Height}");

            // 分析所有轮廓
            for (int i = 0; i < contours.Length; i++)
            {
                var currentContour = contours[i];
                double area = Cv2.ContourArea(currentContour);
                var boundingRect = Cv2.BoundingRect(currentContour);
                Console.WriteLine($"Contour {i + 1}: area={area:F2}, points={currentContour.Length}, bounds=({boundingRect.X},{boundingRect.Y},{boundingRect.Width},{boundingRect.Height})");
            }

            // 计算从掩码坐标到原图坐标的映射关系
            // 根据bounds来映射坐标
            double scaleX = (double)bounds.Width / maskMat.Width;
            double scaleY = (double)bounds.Height / maskMat.Height;
            int offsetX = bounds.X;
            int offsetY = bounds.Y;

            Console.WriteLine($"Coordinate mapping - Scale: ({scaleX:F3}, {scaleY:F3}), Offset: ({offsetX}, {offsetY})");

            // 找到最大的轮廓（类似Python代码的逻辑）
            if (contours.Length == 0)
            {
                Console.WriteLine("No contours found after preprocessing");
                return;
            }

            // 按面积排序，选择最大的轮廓
            var sortedContours = contours
                .Select((contour, index) => new { Contour = contour, Area = Cv2.ContourArea(contour), Index = index })
                .Where(x => x.Area >= 50) // 降低阈值，因为现在是在掩码坐标系中
                .OrderByDescending(x => x.Area)
                .ToList();

            if (sortedContours.Count == 0)
            {
                Console.WriteLine("No contours meet the area threshold");
                return;
            }

            // 处理最大的轮廓（类似Python的逻辑）
            var largestContourInfo = sortedContours.First();
            var contour = largestContourInfo.Contour;
            Console.WriteLine($"Processing largest contour with area: {largestContourInfo.Area:F2}, points: {contour.Length}");

            // 显示轮廓在掩码坐标系中的边界框信息
            var maskBoundingRect = Cv2.BoundingRect(contour);
            Console.WriteLine($"Largest contour mask bounding rect: {maskBoundingRect}");

            // 将轮廓坐标映射到原图坐标系
            Point[] mappedContour = new Point[contour.Length];
            for (int j = 0; j < contour.Length; j++)
            {
                mappedContour[j] = new Point(
                    (int)(contour[j].X * scaleX + offsetX),
                    (int)(contour[j].Y * scaleY + offsetY)
                );
            }

            // 显示映射后的边界框
            var mappedBoundingRect = Cv2.BoundingRect(mappedContour);
            Console.WriteLine($"Largest contour mapped bounding rect: {mappedBoundingRect}");

            // 拟合直线并绘制（使用映射后的坐标）
            var (startPoint, endPoint, angle) = FitAndDrawLine(img, mappedContour);

            // 绘制角度文本
            DrawAngleText(img, startPoint, angle);

            // 清理资源
            maskMat?.Dispose();
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error processing mask contours: {ex.Message}");
        }
    }

    /// <summary>
    /// 将 YoloSharp 的 BitmapBuffer 转换为 OpenCV Mat
    /// </summary>
    private static Mat? ConvertBitmapBufferToMat(Compunet.YoloSharp.Memory.BitmapBuffer mask)
    {
        try
        {
            // 首先探索 BitmapBuffer 的结构
            Console.WriteLine($"BitmapBuffer type: {mask.GetType()}");

            // 使用反射查看可用的属性和方法
            var type = mask.GetType();
            var properties = type.GetProperties();
            var fields = type.GetFields(BindingFlags.NonPublic | BindingFlags.Instance | BindingFlags.Public);

            Console.WriteLine("Available properties:");
            foreach (var prop in properties)
            {
                try
                {
                    var value = prop.GetValue(mask);
                    Console.WriteLine($"  {prop.Name}: {value} (Type: {prop.PropertyType})");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"  {prop.Name}: Error accessing - {ex.Message}");
                }
            }

            Console.WriteLine("Available fields:");
            foreach (var field in fields)
            {
                try
                {
                    var value = field.GetValue(mask);
                    Console.WriteLine($"  {field.Name}: {value} (Type: {field.FieldType})");
                }
                catch (Exception ex)
                {
                    Console.WriteLine($"  {field.Name}: Error accessing - {ex.Message}");
                }
            }

            // 尝试将 BitmapBuffer 转换为 ImageSharp Image
            // 然后再转换为 OpenCV Mat
            Mat? maskMat = ConvertBitmapBufferViaImageSharp(mask);

            if (maskMat != null)
            {
                Console.WriteLine($"Successfully converted mask: {mask.Width}x{mask.Height}");
                return maskMat;
            }

            // 如果上面的方法失败，回退到简单的掩码创建
            Console.WriteLine("Falling back to simple mask creation");
            return CreateFallbackMask(mask);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error converting BitmapBuffer to Mat: {ex.Message}");
            return CreateFallbackMask(mask);
        }
    }

    /// <summary>
    /// 尝试通过 ImageSharp 转换 BitmapBuffer
    /// </summary>
    private static Mat? ConvertBitmapBufferViaImageSharp(Compunet.YoloSharp.Memory.BitmapBuffer mask)
    {
        try
        {
            // 尝试将 BitmapBuffer 转换为 ImageSharp Image
            // 这可能需要根据实际的 YoloSharp 实现调整

            // 检查是否有 ToImage 或类似的方法
            var type = mask.GetType();
            var methods = type.GetMethods();

            Console.WriteLine("Available methods:");
            foreach (var method in methods.Where(m => !m.Name.StartsWith("get_") && !m.Name.StartsWith("set_")))
            {
                Console.WriteLine($"  {method.Name}({string.Join(", ", method.GetParameters().Select(p => p.ParameterType.Name))})");
            }

            // 尝试访问 _buffer 字段（我们发现它包含 Memory<float>）
            var bufferField = type.GetField("_buffer", BindingFlags.NonPublic | BindingFlags.Instance);
            if (bufferField != null)
            {
                var bufferValue = bufferField.GetValue(mask);
                Console.WriteLine($"Found _buffer field: {bufferValue?.GetType()}");

                if (bufferValue is Memory<float> memoryFloat)
                {
                    var span = memoryFloat.Span;
                    float[] floatArray = span.ToArray();
                    Console.WriteLine($"Extracted {floatArray.Length} float values from mask buffer");
                    return CreateMatFromFloatArray(floatArray, mask.Width, mask.Height);
                }
            }

            // 尝试直接访问像素数据
            // 可能 BitmapBuffer 有 Data 属性或类似的
            var dataProperty = type.GetProperty("Data");
            if (dataProperty != null)
            {
                var data = dataProperty.GetValue(mask);
                Console.WriteLine($"Found Data property: {data?.GetType()}");

                if (data is byte[] byteArray)
                {
                    return CreateMatFromByteArray(byteArray, mask.Width, mask.Height);
                }
                else if (data is float[] floatArray)
                {
                    return CreateMatFromFloatArray(floatArray, mask.Width, mask.Height);
                }
            }

            return null;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in ConvertBitmapBufferViaImageSharp: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 从字节数组创建 Mat
    /// </summary>
    private static Mat CreateMatFromByteArray(byte[] data, int width, int height)
    {
        Mat maskMat = new Mat(height, width, MatType.CV_8UC1);

        unsafe
        {
            byte* maskPtr = (byte*)maskMat.DataPointer;
            for (int i = 0; i < Math.Min(data.Length, width * height); i++)
            {
                maskPtr[i] = data[i] > 128 ? (byte)255 : (byte)0;
            }
        }

        Console.WriteLine($"Created Mat from byte array: {width}x{height}");
        return maskMat;
    }

    /// <summary>
    /// 从浮点数组创建 Mat
    /// </summary>
    private static Mat CreateMatFromFloatArray(float[] data, int width, int height)
    {
        Mat maskMat = new Mat(height, width, MatType.CV_8UC1);

        // 首先分析数据的分布
        float minVal = data.Min();
        float maxVal = data.Max();
        float avgVal = data.Average();

        Console.WriteLine($"Mask data stats - Min: {minVal:F4}, Max: {maxVal:F4}, Avg: {avgVal:F4}");

        // 使用自适应阈值，基于数据分布
        // 计算一个更智能的阈值
        float threshold = CalculateOptimalThreshold(data);
        Console.WriteLine($"Using adaptive threshold: {threshold:F4}");

        unsafe
        {
            byte* maskPtr = (byte*)maskMat.DataPointer;

            // 尝试两种数据布局：行优先和列优先
            // 首先尝试行优先（标准布局）
            for (int i = 0; i < Math.Min(data.Length, width * height); i++)
            {
                maskPtr[i] = data[i] > threshold ? (byte)255 : (byte)0;
            }

            // 检查结果是否合理（前几行应该有一些非零值）
            bool hasDataInFirstRows = false;
            for (int y = 0; y < Math.Min(5, height); y++)
            {
                for (int x = 0; x < width; x++)
                {
                    if (maskPtr[y * width + x] > 0)
                    {
                        hasDataInFirstRows = true;
                        break;
                    }
                }
                if (hasDataInFirstRows) break;
            }

            // 数据布局看起来是正确的，不需要重新排列
            // 前几行为空是正常的，说明目标在掩码的其他位置
        }

        // 统计非零像素数量 
        int nonZeroPixels = 0;
        unsafe
        {
            byte* maskPtr = (byte*)maskMat.DataPointer;
            for (int i = 0; i < width * height; i++)
            {
                if (maskPtr[i] > 0) nonZeroPixels++;
            }
        }

        Console.WriteLine($"Created Mat from float array: {width}x{height}, non-zero pixels: {nonZeroPixels}");

        // 保存掩码图像用于调试
        string debugMaskPath = $"debug_mask_{width}x{height}.png";
        Cv2.ImWrite(debugMaskPath, maskMat);
        Console.WriteLine($"Saved debug mask to: {debugMaskPath}");

        // 输出掩码数据的前几个值和分布用于调试
        Console.WriteLine("First 20 mask values:");
        unsafe
        {
            byte* maskPtr = (byte*)maskMat.DataPointer;
            for (int i = 0; i < Math.Min(20, width * height); i++)
            {
                Console.Write($"{maskPtr[i]} ");
                if ((i + 1) % 10 == 0) Console.WriteLine();
            }
            Console.WriteLine();

            // 也检查中间和末尾的值
            int totalPixels = width * height;
            Console.WriteLine("Middle 20 mask values:");
            int midStart = totalPixels / 2 - 10;
            for (int i = midStart; i < Math.Min(midStart + 20, totalPixels); i++)
            {
                Console.Write($"{maskPtr[i]} ");
                if ((i - midStart + 1) % 10 == 0) Console.WriteLine();
            }
            Console.WriteLine();

            Console.WriteLine("Last 20 mask values:");
            int endStart = Math.Max(0, totalPixels - 20);
            for (int i = endStart; i < totalPixels; i++)
            {
                Console.Write($"{maskPtr[i]} ");
                if ((i - endStart + 1) % 10 == 0) Console.WriteLine();
            }
            Console.WriteLine();
        }

        // 检查原始float数据的分布
        Console.WriteLine("Original float data distribution:");
        int[] ranges = new int[5]; // 0-0.2, 0.2-0.4, 0.4-0.6, 0.6-0.8, 0.8-1.0
        for (int i = 0; i < data.Length; i++)
        {
            int rangeIndex = Math.Min(4, (int)(data[i] * 5));
            ranges[rangeIndex]++;
        }
        for (int i = 0; i < ranges.Length; i++)
        {
            Console.WriteLine($"Range {i * 0.2:F1}-{(i + 1) * 0.2:F1}: {ranges[i]} pixels");
        }

        return maskMat;
    }

    /// <summary>
    /// 计算最优阈值
    /// </summary>
    private static float CalculateOptimalThreshold(float[] data)
    {
        // 使用Otsu方法的简化版本
        var sortedData = data.OrderBy(x => x).ToArray();

        // 找到数据的分布特征
        float q1 = sortedData[(int)(sortedData.Length * 0.25)];
        float median = sortedData[sortedData.Length / 2];
        float q3 = sortedData[(int)(sortedData.Length * 0.75)];
        float mean = data.Average();

        Console.WriteLine($"Data distribution - Q1: {q1:F4}, Median: {median:F4}, Q3: {q3:F4}, Mean: {mean:F4}");

        // 如果数据有明显的双峰分布，使用中位数作为阈值
        // 否则使用较高的阈值来获得更精确的轮廓
        float threshold;
        if (mean > 0.7f && q3 > 0.8f)
        {
            // 高置信度数据，使用较高阈值
            threshold = Math.Max(0.9f, q3);
        }
        else
        {
            // 使用中位数和均值之间的值
            threshold = (median + mean) / 2.0f;
        }

        return Math.Min(0.95f, Math.Max(0.5f, threshold));
    }

    /// <summary>
    /// 预处理掩码以获得更好的轮廓
    /// </summary>
    private static Mat PreprocessMask(Mat maskMat)
    {
        try
        {
            // 创建处理后的掩码副本
            Mat processedMask = maskMat.Clone();

            // 应用形态学操作来清理掩码
            // 1. 开运算：去除小的噪声点
            Mat kernel = Cv2.GetStructuringElement(MorphShapes.Ellipse, new OpenCvSharp.Size(3, 3));
            Cv2.MorphologyEx(processedMask, processedMask, MorphTypes.Open, kernel);

            // 2. 闭运算：填充小的空洞
            Cv2.MorphologyEx(processedMask, processedMask, MorphTypes.Close, kernel);

            // 3. 边缘检测来获得更精确的轮廓
            Mat edges = new Mat();
            Cv2.Canny(processedMask, edges, 50, 150);

            // 4. 膨胀边缘以确保轮廓连续
            Cv2.Dilate(edges, edges, kernel, iterations: 1);

            // 清理资源
            kernel.Dispose();
            processedMask.Dispose();

            Console.WriteLine($"Applied morphological operations and edge detection");

            return edges;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error in mask preprocessing: {ex.Message}");
            return maskMat.Clone();
        }
    }

    /// <summary>
    /// 创建回退掩码
    /// </summary>
    private static Mat CreateFallbackMask(Compunet.YoloSharp.Memory.BitmapBuffer mask)
    {
        Mat maskMat = new Mat(mask.Height, mask.Width, MatType.CV_8UC1);
        maskMat.SetTo(new Scalar(0));

        // 创建一个更复杂的测试掩码，模拟真实的分割结果
        int centerX = mask.Width / 2;
        int centerY = mask.Height / 2;

        // 创建一个倾斜的矩形掩码来测试角度计算
        Point[] maskPoints =
        [
            new(centerX - mask.Width/3, centerY - mask.Height/4),
            new(centerX + mask.Width/3, centerY - mask.Height/6),
            new(centerX + mask.Width/3, centerY + mask.Height/6),
            new(centerX - mask.Width/3, centerY + mask.Height/4)
        ];

        Cv2.FillPoly(maskMat, new Point[][] { maskPoints }, new Scalar(255));

        Console.WriteLine($"Created fallback tilted mask: {mask.Width}x{mask.Height}");
        return maskMat;
    }

    /// <summary>
    /// 拟合直线并计算角度（不绘制）
    /// </summary>
    private static double? FitLineAndGetAngle(OpenCvSharp.Point[] contour)
    {
        try
        {
            if (contour.Length < 2)
            {
                Console.WriteLine("Contour has too few points for line fitting");
                return null;
            }

            // 使用 OpenCV 的 fitLine 函数拟合直线
            Line2D line = Cv2.FitLine(contour, DistanceTypes.L2, 0, 0.01, 0.01);
            
            float vx = (float)line.Vx;  // 方向向量 x 分量
            float vy = (float)line.Vy;  // 方向向量 y 分量

            // 计算水平角度
            double angle = Math.Atan2(vy, vx) * 180.0 / Math.PI;
            if (angle < 0)
            {
                angle = Math.Abs(angle);
            }

            Console.WriteLine($"Fitted line angle: {angle:F2}°");
            return angle;
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error fitting line: {ex.Message}");
            return null;
        }
    }

    /// <summary>
    /// 拟合直线并绘制到 img 上，返回端点与水平夹角
    /// </summary>
    private static (OpenCvSharp.Point pt1, OpenCvSharp.Point pt2, double angle) FitAndDrawLine(Mat img, OpenCvSharp.Point[] contour)
    {
        try
        {
            if (contour.Length < 2)
            {
                Console.WriteLine("Contour has too few points for line fitting");
                return (new Point(0, 0), new Point(0, 0), 0.0);
            }

            // 获取轮廓的边界矩形 - 这样更准确
            Rect boundingRect = Cv2.BoundingRect(contour);

            int xMin = boundingRect.X;
            int yMin = boundingRect.Y;
            int xMax = boundingRect.X + boundingRect.Width;
            int yMax = boundingRect.Y + boundingRect.Height;

            float xMean = (xMin + xMax) / 2.0f;
            float yMean = (yMin + yMax) / 2.0f;

            Console.WriteLine($"Contour bounding rect: ({xMin}, {yMin}) to ({xMax}, {yMax}), center: ({xMean:F1}, {yMean:F1})");

            // 使用 OpenCV 的 fitLine 函数拟合直线
            Line2D line = Cv2.FitLine(contour, DistanceTypes.L2, 0, 0.01, 0.01);

            float vx = (float)line.Vx;  // 方向向量 x 分量
            float vy = (float)line.Vy;  // 方向向量 y 分量
            float x0 = (float)line.X1;  // 直线上一点的 x 坐标
            float y0 = (float)line.Y1;  // 直线上一点的 y 坐标

            // 计算直线在轮廓边界矩形内的端点
            // 与你的Python代码逻辑完全一致
            float t1, t2;
            if (Math.Abs(vx) > Math.Abs(vy))  // 主要沿 x 方向
            {
                t1 = (xMin - x0) / vx;
                t2 = (xMax - x0) / vx;
            }
            else  // 主要沿 y 方向
            {
                t1 = (yMin - y0) / vy;
                t2 = (yMax - y0) / vy;
            }

            var startPoint = new Point((int)(x0 + t1 * vx), (int)(y0 + t1 * vy));
            var endPoint = new Point((int)(x0 + t2 * vx), (int)(y0 + t2 * vy));

            // 确保端点在边界矩形内
            startPoint.X = Math.Max(xMin, Math.Min(xMax, startPoint.X));
            startPoint.Y = Math.Max(yMin, Math.Min(yMax, startPoint.Y));
            endPoint.X = Math.Max(xMin, Math.Min(xMax, endPoint.X));
            endPoint.Y = Math.Max(yMin, Math.Min(yMax, endPoint.Y));

            Console.WriteLine($"Line endpoints: ({startPoint.X}, {startPoint.Y}) to ({endPoint.X}, {endPoint.Y})");

            // 绘制直线
            Cv2.Line(img, startPoint, endPoint, new Scalar(0, 255, 0), 2);

            // 计算水平角度
            double angle = Math.Atan2(vy, vx) * 180.0 / Math.PI;
            if (angle < 0)
            {
                angle = Math.Abs(angle);
            }
            Cv2.PutText(img, $"Angle: {angle:F2} deg", new Point(startPoint.X, startPoint.Y + 30),
                       HersheyFonts.HersheySimplex, 0.6, new Scalar(255, 255, 0), 2);
            Console.WriteLine($"Fitted line angle: {angle:F2}°");

            return (startPoint, endPoint, angle);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error fitting line: {ex.Message}");
            return (new Point(0, 0), new Point(0, 0), 0.0);
        }
    }

    /// <summary>
    /// 绘制角度文本
    /// </summary>
    private static void DrawAngleText(Mat img, Point startPoint, double angle)
    {
        try
        {
            string text = $"{angle:F2} deg";

            // 确保文本绘制在图像范围内
            int textX = Math.Max(0, Math.Min(startPoint.X, img.Width - 80));
            int textY = Math.Max(20, Math.Min(startPoint.Y - 10, img.Height - 10));

            Cv2.PutText(img, text, new Point(textX, textY),
                       HersheyFonts.HersheySimplex, 0.6, new Scalar(255, 255, 0), 2);
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error drawing angle text: {ex.Message}");
        }
    }

    /// <summary>
    /// 简化版本：直接在边界框区域绘制掩码轮廓
    /// </summary>
    /// <param name="img">原图</param>
    /// <param name="mask">分割掩码</param>
    /// <param name="bounds">边界框</param>
    /// <param name="color">轮廓颜色</param>
    /// <param name="thickness">轮廓线条粗细</param>
    public static void DrawMaskContour(Mat img, Compunet.YoloSharp.Memory.BitmapBuffer mask, SixLabors.ImageSharp.Rectangle bounds, Scalar? color = null, int thickness = 2)
    {
        try
        {
            Scalar contourColor = color ?? new Scalar(0, 255, 0); // 默认绿色

            // 将 BitmapBuffer 转换为 OpenCV Mat
            Mat? maskMat = ConvertBitmapBufferToMat(mask);

            if (maskMat == null || maskMat.Empty())
            {
                Console.WriteLine("Failed to convert mask for contour drawing");
                return;
            }

            // 查找轮廓
            Cv2.FindContours(maskMat, out Point[][] contours, out HierarchyIndex[] hierarchy,
                RetrievalModes.External, ContourApproximationModes.ApproxSimple);

            if (contours.Length == 0)
            {
                Console.WriteLine("No contours found in mask for drawing");
                maskMat?.Dispose();
                return;
            }

            // 计算坐标映射
            double scaleX = (double)bounds.Width / maskMat.Width;
            double scaleY = (double)bounds.Height / maskMat.Height;

            // 绘制所有轮廓
            foreach (var contour in contours)
            {
                // 将轮廓坐标映射到原图坐标系
                Point[] mappedContour = new Point[contour.Length];
                for (int i = 0; i < contour.Length; i++)
                {
                    mappedContour[i] = new Point(
                        (int)(contour[i].X * scaleX + bounds.X),
                        (int)(contour[i].Y * scaleY + bounds.Y)
                    );
                }

                // 绘制轮廓
                Cv2.DrawContours(img, new Point[][] { mappedContour }, -1, contourColor, thickness);
            }

            maskMat?.Dispose();
            Console.WriteLine($"Drew {contours.Length} mask contours");
        }
        catch (Exception ex)
        {
            Console.WriteLine($"Error drawing mask contour: {ex.Message}");
        }
    }
}
