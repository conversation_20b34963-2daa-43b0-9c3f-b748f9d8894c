<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <appSettings>
    <!-- 相机管理器类型配置 -->
    <!-- Original: 使用原始的 CameraManager（复杂缓存机制） -->
    <!-- Enhanced: 使用新的 CameraManager2（参考 BasicDemoLineScan） -->
    <add key="CameraManagerType" value="Original" />
    
    <!-- 相机参数配置 -->
    <add key="Camera.ExposureTime" value="2000.0" />
    <add key="Camera.Gain" value="20.0" />
    <add key="Camera.PixelFormat" value="Mono8" />
    <add key="Camera.AutoExposureEnabled" value="false" />
    <add key="Camera.AutoGainEnabled" value="false" />
    
    <!-- 传送带相关配置 -->
    <add key="ConveyorBelt.Speed" value="0.3" />
    <add key="Sensor.TriggerDelayCompensation" value="50" />
    <add key="Photo.MinIntervalMs" value="10" />
    
    <!-- 图像保存配置 -->
    <add key="Image.OutputDirectory" value="Images" />
    <add key="Image.DefaultFormat" value="Jpeg" />
    <add key="Image.JpegQuality" value="90" />
    
    <!-- 日志配置 -->
    <add key="Log.Level" value="Info" />
    <add key="Log.EnableFileLogging" value="true" />
    
    <!-- 网络配置（GigE设备） -->
    <add key="Network.PacketSize" value="auto" />
    <add key="Network.HeartbeatTimeout" value="3000" />
    
    <!-- 性能配置 -->
    <add key="Performance.PreviewFrameRate" value="10" />
    <add key="Performance.MaxMemoryUsageMB" value="1024" />
  </appSettings>
  
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
  </startup>
</configuration>
