﻿using GGEC.AOI.T68ZJ.Client.Helper;
using GGEC.AOI.T68ZJ.Client.Mqtt;
using GGEC.AOI.T68ZJ.Config;
using GGEC.AOI.T68ZJ.Extension;
using GGEC.AOI.T68ZJ.Log;
using GGEC.AOI.T68ZJ.Managers;
using Newtonsoft.Json;
using System.Diagnostics;
using System.Text;
using System.IO;

namespace GGEC.AOI.T68ZJ.Client
{
    /// <summary>
    /// 统计数据持久化类
    /// </summary>
    public class StatisticsData
    {
        public int TotalCount { get; set; }
        public int PassCount { get; set; }
        public int FailCount { get; set; }
        public DateTime LastUpdated { get; set; }
    }

    public partial class MainForm : Form
    {
        // 应用程序配置
        private readonly ApplicationConfig _appConfig;

        private ImageProcessingManager _processingManager;
        private SimpleMqttManager? mqttManager;
        private AsyncBusinessManager _asyncBusinessManager;
        private ICameraManager? cameraManager;

        // 统计变量
        private int totalCount = 0;
        private int passCount = 0;
        private int failCount = 0;

        // 推理开关控制
        private bool enableInference = true;

        // 统计数据文件路径
        private readonly string statisticsFilePath = Path.Combine(Application.StartupPath, "statistics.json");
        public MainForm()
        {
            InitializeComponent();

            // 加载应用程序配置
            _appConfig = AppConfig.LoadConfig();

            // 创建简化的处理管理器
            var config = SimpleConfig.LoadConfig();
            var rules = new DetectionRules();
            _processingManager = new ImageProcessingManager(config, rules);

            // 简单初始化异步管理器
            _asyncBusinessManager = new AsyncBusinessManager(_processingManager);

            // 初始化相机管理器
            InitializeCameraManager();

            // 优化相机参数用于传送带拍照
            OptimizeCameraParametersForConveyor();

            InitializeMqtt();

            pictureBox1.SizeMode = PictureBoxSizeMode.Zoom;
            pictureBox2.SizeMode = PictureBoxSizeMode.Zoom;

            // 加载统计数据
            LoadStatisticsData();

            // 初始化统计显示
            UpdateStatisticsDisplay();

            // 绑定清零按钮事件
            btnClearStats.Click += BtnClearStats_Click;

            // 初始化推理开关状态
            chkEnableInference.Checked = enableInference;

            // 启动预热检测（异步执行，不阻塞界面加载）
            _ = Task.Run(async () =>
            {
                await Task.Delay(2000); // 等待2秒让界面完全加载
                await PerformWarmupDetection();
            });

            // 为测试添加一些按钮事件（可以通过现有按钮测试统计功能）
            // button1现在可以用来测试OK结果
            // button2现在可以用来测试NG结果

            //  this.FormClosing += MainForm3_FormClosing;
            //this.Load += MainForm3_Load;
            //this.Shown += MainForm3_Shown;
            //this.FormClosed += MainForm3_FormClosed;
            this.FormClosing += (s, e) =>
            {
                // 释放PictureBox中的图像资源
                pictureBox1.Image?.Dispose();
                pictureBox2.Image?.Dispose();
                pictureBox3.Image?.Dispose();
                pictureBox4.Image?.Dispose();

                cameraManager?.StopLivePreview();
                cameraManager?.CloseDevices();
                CameraManagerFactory.Dispose();
                _processingManager?.Dispose();
            };
        }

        /// <summary>
        /// 初始化相机管理器
        /// </summary>
        private void InitializeCameraManager()
        {
            try
            {
                Logger.Info("开始初始化相机管理器");

                // 使用配置文件中的相机管理器类型并加载配置
                var cameraManagerType = _appConfig.CameraManager.GetManagerType();
                CameraManagerFactory.InitializeWithConfig(cameraManagerType, _appConfig);

                // 获取相机管理器实例
                cameraManager = CameraManagerFactory.GetInstance();

                // 初始化相机系统
                cameraManager.InitCameras();

                // 打开设备
                cameraManager.OpenDevices(2);

                Logger.Info($"相机管理器初始化完成，类型：{CameraManagerFactory.CurrentType}，设备数量：{cameraManager.DeviceCount}");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "初始化相机管理器失败");
                throw;
            }
        }

        /// <summary>
        /// 优化相机参数用于传送带拍照
        /// </summary>
        private void OptimizeCameraParametersForConveyor()
        {
            try
            {
                Logger.Info("开始优化相机参数用于传送带拍照");

                if (cameraManager == null)
                {
                    Logger.Error("相机管理器未初始化");
                    return;
                }

                // 设置传送带速度（可根据实际情况调整）
                cameraManager.ConveyorBeltSpeed = 0.3; // 0.3米/秒

                // 根据传送带速度自动调整参数
                cameraManager.AutoAdjustForConveyorSpeed();

                Logger.Info("相机参数优化完成");
                Logger.Info($"当前设置 - 传送带速度: {cameraManager.ConveyorBeltSpeed}m/s, " +
                           $"曝光时间: {cameraManager.ExposureTime}μs, " +
                           $"增益: {cameraManager.Gain}, " +
                           $"延迟补偿: {cameraManager.SensorTriggerDelayCompensation}ms");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "优化相机参数时发生异常");
            }
        }

        /// <summary>
        /// 执行异步检测 - 使用同步拍照，专注耗时统计
        /// </summary>
        private async Task ExecuteAsyncDetection()
        {
            var totalStopwatch = Stopwatch.StartNew();

            try
            {
                Logger.Info("=== 开始异步检测工作流（传感器触发）===");
                Logger.Info($"推理开关状态: {(enableInference ? "启用" : "禁用")}");

                // 步骤1: 传感器触发的同步拍照 - 确保两个相机在同一时刻、同一角度拍照
                var cameraStopwatch = Stopwatch.StartNew();
                var (image1, image2) = await cameraManager.TakeSensorTriggeredPhotosAsync();
                cameraStopwatch.Stop();
                Logger.Info($"【耗时统计】传感器触发同步拍照耗时：{cameraStopwatch.ElapsedMilliseconds}ms");

                if (image1 == null || image2 == null)
                {
                    Logger.Error($"同步拍照失败 - 相机1：{(image1 != null ? "成功" : "失败")}，相机2：{(image2 != null ? "成功" : "失败")}");
                    SafeUpdateUI(() =>
                    {
                        lb_jg.Text = "拍照失败";
                        tb_lsjl.Text = $"传感器触发拍照失败 - 相机1：{(image1 != null ? "成功" : "失败")}，相机2：{(image2 != null ? "成功" : "失败")}";
                    });
                    return;
                }

                // 步骤2: 根据开关决定是否执行推理
                DetectionSummary summary;
                var processingStopwatch = Stopwatch.StartNew();

                if (enableInference)
                {
                    // 执行AI推理
                    Logger.Info("执行AI推理检测");
                    summary = await _processingManager.ProcessDualCameraImagesAsync(image1, image2);
                    processingStopwatch.Stop();
                    Logger.Info($"【耗时统计】异步图像处理耗时：{processingStopwatch.ElapsedMilliseconds}ms");
                }
                else
                {
                    // 不执行推理，只显示图像
                    Logger.Info("跳过AI推理，仅显示图像");
                    summary = CreateDisplayOnlySummary(image1, image2);
                    processingStopwatch.Stop();
                    Logger.Info($"【耗时统计】图像显示准备耗时：{processingStopwatch.ElapsedMilliseconds}ms");
                }

                // 步骤3: UI更新
                var uiStopwatch = Stopwatch.StartNew();

                if (enableInference)
                {
                    // 更新检测结果和统计
                    UpdateDetectionResult(summary.OverallResult);
                }

                SafeUpdateUI(() =>
                {
                    if (enableInference)
                    {
                        DisplayAnnotatedImages(summary);
                        // 显示详细检测结果和置信度
                        string detailMessage = BuildDetailedResultMessage(summary);
                        tb_lsjllist.Text = detailMessage + "\r\n" + tb_lsjllist.Text;
                        tb_lsjl.Text = detailMessage;

                        // 根据检测结果设置文本颜色
                        if (summary.OverallResult)
                        {
                            tb_lsjl.ForeColor = Color.White; // OK时显示白色
                        }
                        else
                        {
                            tb_lsjl.ForeColor = Color.Red; // NG时显示红色
                        }

                        tb_lsjllist.ScrollToCaret();
                        // 更新角度显示
                        UpdateAngleDisplay(summary);
                    }
                    else
                    {
                        // 仅显示原始图像
                        DisplayOriginalImages(image1, image2);
                        string displayMessage = $"图像显示模式（未执行推理）- 时间：{DateTime.Now:HH:mm:ss}";
                        tb_lsjllist.Text = displayMessage + "\r\n" + tb_lsjllist.Text;
                        tb_lsjl.Text = displayMessage;
                        tb_lsjl.ForeColor = Color.White; // 默认白色
                        tb_lsjllist.ScrollToCaret();
                        lb_jg.Text = "仅显示";
                    }
                });
                uiStopwatch.Stop();
                Logger.Info($"【耗时统计】UI更新耗时：{uiStopwatch.ElapsedMilliseconds}ms");

                totalStopwatch.Stop();
                Logger.Info($"【耗时统计】总耗时：{totalStopwatch.ElapsedMilliseconds}ms");
                Logger.Info("=== 异步检测工作流完成 ===");

                // 如果检测失败，异步发送MQTT控制消息
                //if (!summary.OverallResult)
                //{
                //    _ = Task.Run(async () =>
                //    {
                //        await Task.Delay(6100);
                //        await SendDetectionFailureControlMessagesAsync();
                //    });
                //}
            }
            catch (Exception ex)
            {
                totalStopwatch.Stop();
                Logger.Exception(ex, $"异步检测异常，总耗时：{totalStopwatch.ElapsedMilliseconds}ms");
                SafeUpdateUI(() =>
                {
                    MessageBox.Show($"异步检测异常：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                });
            }
        }

        /// <summary>
        /// 构建详细结果消息，包含检测项和置信度
        /// </summary>
        private string BuildDetailedResultMessage(DetectionSummary summary)
        {
            var message = new System.Text.StringBuilder();
            message.AppendLine("======================");
            message.AppendLine($"检测完成！{DateTime.Now:HH:mm:ss}");
            message.AppendLine("======================");

            // 综合结果标识
            var overallResultText = summary.OverallResult ? "OK" : "【NG】";
            message.AppendLine($"综合结果：{overallResultText}");
            message.AppendLine($"总耗时：{summary.TotalProcessingTime:F2}ms");
            message.AppendLine();

            // 点位检测结果
            message.AppendLine("--- 点位检测结果 ---");

            var point1Text = summary.Point1Result.IsOk ? "OK" : "【NG】";
            message.AppendLine($"• 点位1：{point1Text}");
            message.AppendLine($"  耗时：{summary.Point1Result.ProcessingTime}ms");
            if (!string.IsNullOrEmpty(summary.Point1Result.Message))
                message.AppendLine($"  原因：{summary.Point1Result.Message}");
            message.AppendLine();

            var point2Text = summary.Point2Result.IsOk ? "OK" : "【NG】";
            message.AppendLine($"• 点位2：{point2Text}");
            message.AppendLine($"  耗时：{summary.Point2Result.ProcessingTime}ms");
            if (!string.IsNullOrEmpty(summary.Point2Result.Message))
                message.AppendLine($"  原因：{summary.Point2Result.Message}");
            message.AppendLine();

            var point3Text = summary.Point3Result.IsOk ? "OK" : "【NG】";
            message.AppendLine($"• 点位3：{point3Text}");
            message.AppendLine($"  耗时：{summary.Point3Result.ProcessingTime}ms");
            if (!string.IsNullOrEmpty(summary.Point3Result.Message))
                message.AppendLine($"  原因：{summary.Point3Result.Message}");
            message.AppendLine();

            var pinTiltText = summary.PinTiltResult.IsOk ? "OK" : "【NG】";
            message.AppendLine($"• 针脚倾斜：{pinTiltText}");
            message.AppendLine($"  耗时：{summary.PinTiltResult.ProcessingTime}ms");
            if (!string.IsNullOrEmpty(summary.PinTiltResult.Message))
                message.AppendLine($"  详情：{summary.PinTiltResult.Message}");
            message.AppendLine();

            // 相机1检测详情（包含角度信息）
            if (summary.Camera1Result?.Detections?.Count > 0)
            {
                message.AppendLine("--- 相机1检测详情 ---");
                foreach (var detection in summary.Camera1Result.Detections)
                {
                    string detectionInfo = $"• {detection.Key}: {detection.Value:P2}";

                    // 添加角度信息
                    if ((detection.Key.ToLower() == "cok2" || detection.Key.ToLower() == "fok2") &&
                        summary.Camera1Result.PinInfos?.Count > 0)
                    {
                        var angles = summary.Camera1Result.PinInfos
                            .Where(p => p.Angle > 0)
                            .Select(p => p.Angle.ToString("F1") + "°")
                            .ToArray();

                        if (angles.Length > 0)
                        {
                            detectionInfo += $" [角度: {string.Join(", ", angles)}]";
                        }
                    }

                    message.AppendLine(detectionInfo);
                }
                message.AppendLine();
            }

            // 相机2检测详情（包含角度信息）
            if (summary.Camera2Result?.Detections?.Count > 0)
            {
                message.AppendLine("--- 相机2检测详情 ---");
                foreach (var detection in summary.Camera2Result.Detections)
                {
                    string detectionInfo = $"• {detection.Key}: {detection.Value:P2}";

                    // 添加角度信息
                    if ((detection.Key.ToLower() == "cok2" || detection.Key.ToLower() == "fok2") &&
                        summary.Camera2Result.PinInfos?.Count > 0)
                    {
                        var angles = summary.Camera2Result.PinInfos
                            .Where(p => p.Angle > 0)
                            .Select(p => p.Angle.ToString("F1") + "° [" + p.Distance.ToString("F1") + "]")
                            .ToArray();

                        if (angles.Length > 0)
                        {
                            detectionInfo += $" [角度: {string.Join(", ", angles)}]";
                        }
                    }

                    message.AppendLine(detectionInfo);
                }
                message.AppendLine();
            }

            // 单独显示角度汇总信息
            var allAngles = new List<(string camera, string type, double angle, double distance)>();

            if (summary.Camera1Result?.PinInfos?.Count > 0)
            {
                foreach (var pin in summary.Camera1Result.PinInfos.Where(p => p.Angle > 0))
                {
                    var detectionType = summary.Camera1Result.Detections
                        .Where(d => d.Key.ToLower() == "cok2" || d.Key.ToLower() == "fok2")
                        .FirstOrDefault().Key ?? "未知";
                    allAngles.Add(("相机1", detectionType, pin.Angle, 0));
                }
            }

            if (summary.Camera2Result?.PinInfos?.Count > 0)
            {
                foreach (var pin in summary.Camera2Result.PinInfos.Where(p => p.Angle > 0))
                {
                    var detectionType = summary.Camera2Result.Detections
                        .Where(d => d.Key.ToLower() == "cok2" || d.Key.ToLower() == "fok2")
                        .FirstOrDefault().Key ?? "未知";
                    allAngles.Add(("相机2", detectionType, pin.Angle, pin.Distance));
                }
            }

            if (allAngles.Count > 0)
            {
                message.AppendLine("--- 角度检测汇总 ---");
                foreach (var (camera, type, angle, distance) in allAngles)
                {
                    message.AppendLine($"• {camera} {type}: {angle:F1}°[{distance}]");
                }
                message.AppendLine();
            }

            return message.ToString();
        }

        private async void InitializeMqtt()
        {
            mqttManager = new SimpleMqttManager(
                serverEndpoint: _appConfig.Mqtt.ServerEndpoint,
                serverPort: _appConfig.Mqtt.ServerPort,
                clientId: _appConfig.Mqtt.ClientId);
            // 注册事件处理器
            mqttManager.MessageReceived += (topic, message) =>
            {
                Logger.Info($"收到消息 - 主题: {topic}, 内容: {message}");
                if (topic != null && topic.Equals(_appConfig.Mqtt.UploadTopic))
                {
                    var jsonstr = message.ToString();
                    var json = JsonConvert.DeserializeObject<dynamic>(jsonstr);
                    if (json != null && json.DI1 == "1")
                    {
                        Logger.Info("Recv DI01 Message.");
                        // 异步执行检测，不阻塞MQTT消息处理
                        _ = ExecuteAsyncDetection();
                    }
                }

            };

            mqttManager.ConnectionChanged += (isConnected) =>
            {
                Logger.Info($"连接状态变化: {(isConnected ? "已连接" : "已断开")}");
            };

            mqttManager.ErrorOccurred += (error) =>
            {
                Logger.Info($"错误: {error}");
            };

            try
            {
                // 连接到MQTT服务器
                Logger.Info("正在连接到MQTT服务器...");
                bool connected = await mqttManager.ConnectAsync();

                if (connected)
                {
                    Logger.Info("连接成功！");

                    // 订阅主题
                    Logger.Info("订阅主题...");
                    await mqttManager.SubscribeAsync("/UploadData");
                    //await mqttManager.SubscribeAsync("/DownloadTopic");

                    // 发送消息
                    //Logger.Info("发送消息...");
                    //await mqttManager.PublishAsync("test/topic1", "Hello from SimpleMqttManager!");
                    //await mqttManager.PublishAsync("test/topic2", "这是一条中文消息");

                    // 等待接收消息
                    Logger.Info("等待接收消息...");
                    //await Task.Delay(3000);

                    // 取消订阅
                    //Logger.Info("取消订阅...");
                    //await mqttManager.UnsubscribeAsync("test/topic1");

                }
                else
                {
                    Logger.Info("连接失败");
                }
            }
            catch (Exception ex)
            {
                Logger.Info($"运行异常: {ex.Message}");
            }
        }

        private void bt_video_Click(object sender, EventArgs e)
        {
            try
            {
                if (cameraManager?.ShowLivePreview == true)
                {
                    // 停止预览
                    cameraManager.StopLivePreview();
                    bt_video.Text = "开始预览";

                    // 清空PictureBox显示并重置为默认颜色
                    pictureBox1.Image?.Dispose();
                    pictureBox1.Image = null;
                    pictureBox1.BackColor = SystemColors.Control;

                    pictureBox2.Image?.Dispose();
                    pictureBox2.Image = null;
                    pictureBox2.BackColor = SystemColors.Control;

                    // 显示内存使用情况
                    Logger.Info($"停止预览后内存使用: {cameraManager.GetMemoryUsage()}");
                }
                else
                {
                    if (cameraManager == null)
                    {
                        Logger.Error("相机管理器未初始化");
                        MessageBox.Show("相机管理器未初始化", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                        return;
                    }

                    // 开始预览
                    bt_video.Text = "预览中...";

                    // 清空PictureBox显示
                    pictureBox1.Image?.Dispose();
                    pictureBox1.Image = null;
                    pictureBox2.Image?.Dispose();
                    pictureBox2.Image = null;

                    // 显示加载提示
                    pictureBox1.LoadAsync("Resources/Icon/加载中.png");
                    pictureBox2.LoadAsync("Resources/Icon/加载中2.png");

                    // 显示预览前内存使用情况
                    Logger.Info($"开始预览前内存使用: {cameraManager.GetMemoryUsage()}");

                    cameraManager.ShowLivePreview = true;
                    // 使用内存优化的ImageRender预览方式
                    cameraManager.StartLivePreview(pictureBox1, pictureBox2);
                    bt_video.Text = "停止预览";
                }
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "预览操作失败");
                MessageBox.Show($"预览操作失败：{ex.Message}", "错误", MessageBoxButtons.OK, MessageBoxIcon.Error);
                bt_video.Text = "开始预览";
            }
        }

        private async void bt_imgcheck_Click(object sender, EventArgs e)
        {
            // 异步执行检测，显示详细耗时统计
            await ExecuteAsyncDetection();
        }

        /// <summary>
        /// 推理开关状态改变事件处理
        /// </summary>
        private void chkEnableInference_CheckedChanged(object sender, EventArgs e)
        {
            enableInference = chkEnableInference.Checked;
            Logger.Info($"推理开关状态改变: {(enableInference ? "启用" : "禁用")}");

            SafeUpdateUI(() =>
            {
                tb_lsjl.Text = $"推理功能已{(enableInference ? "启用" : "禁用")}";
            });
        }

        /// <summary>
        /// 创建仅用于显示的检测摘要（不执行推理）
        /// </summary>
        private DetectionSummary CreateDisplayOnlySummary(MvCameraControl.IImage image1, MvCameraControl.IImage image2)
        {
            return new DetectionSummary
            {
                OverallResult = true, // 默认为OK，因为没有执行检测
                TotalProcessingTime = 0,
                Point1Result = new PointDetectionResult { IsOk = true, ProcessingTime = 0 },
                Point2Result = new PointDetectionResult { IsOk = true, ProcessingTime = 0 },
                Point3Result = new PointDetectionResult { IsOk = true, ProcessingTime = 0 },
                PinTiltResult = new PointDetectionResult { IsOk = true, ProcessingTime = 0, Message = "未执行检测" },
                Camera1Result = new InferenceResult
                {
                    Detections = new List<KeyValuePair<string, float>>(),
                    PinInfos = new List<PinInfo>()
                },
                Camera2Result = new InferenceResult
                {
                    Detections = new List<KeyValuePair<string, float>>(),
                    PinInfos = new List<PinInfo>()
                }
            };
        }

        /// <summary>
        /// 显示原始图像（不带标注）
        /// </summary>
        private void DisplayOriginalImages(MvCameraControl.IImage image1, MvCameraControl.IImage image2)
        {
            try
            {
                // 释放之前的图像
                pictureBox1.Image?.Dispose();
                pictureBox2.Image?.Dispose();

                // 将IImage转换为Bitmap并显示
                pictureBox1.Image = ConvertIImageToBitmap(image1);
                pictureBox2.Image = ConvertIImageToBitmap(image2);

                Logger.Info("原始图像显示完成");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "显示原始图像时发生异常");
            }
        }

        /// <summary>
        /// 将IImage转换为Bitmap
        /// </summary>
        private Bitmap? ConvertIImageToBitmap(MvCameraControl.IImage? image)
        {
            if (image == null) return null;

            try
            {
                // 使用扩展方法转换
                return image.ToBitmap();
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "转换IImage到Bitmap时发生异常");
                return null;
            }
        }

        private void button1_Click(object sender, EventArgs e)
        {
            // 拍照 - 相机1
            TakePhoto(0, pictureBox1);
        }

        private void button2_Click(object sender, EventArgs e)
        {
            // 拍照 - 相机2
            TakePhoto(1, pictureBox2);
        }

        private async void button3_Click(object sender, EventArgs e)
        {
            // 异步同时拍照两个相机
            var stopwatch = Stopwatch.StartNew();
            var task1 = Task.Run(() => TakePhoto(0, pictureBox1));
            var task2 = Task.Run(() => TakePhoto(1, pictureBox2));
            await Task.WhenAll(task1, task2);
            stopwatch.Stop();
            Logger.Info($"【耗时统计】并行拍照耗时：{stopwatch.ElapsedMilliseconds}ms");
        }

        /// <summary>
        /// 拍照功能
        /// </summary>
        private void TakePhoto(int deviceIndex, PictureBox targetPictureBox)
        {
            try
            {
                var image = CameraManager.Instance.TakePhoto(deviceIndex);
                if (image != null)
                {
                    var bitmap = image.ToBitmap();
                    if (bitmap != null)
                    {
                        // 更新PictureBox显示
                        var oldImage = targetPictureBox.Image;
                        targetPictureBox.Image = bitmap;
                        oldImage?.Dispose();

                        // 可选：保存图像到文件
                        string fileName = $"Camera_{deviceIndex}_{DateTime.Now:yyyyMMdd_HHmmss}.jpg";
                        bitmap.Save(fileName, System.Drawing.Imaging.ImageFormat.Jpeg);

                        MessageBox.Show($"拍照成功！图像已保存为：{fileName}", "拍照成功",
                                      MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }
                    else
                    {
                        MessageBox.Show($"相机 {deviceIndex + 1} 图像转换失败", "拍照失败",
                                      MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                }
                else
                {
                    MessageBox.Show($"相机 {deviceIndex + 1} 没有可用图像，请确保预览已开启", "拍照失败",
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"拍照失败：{ex.Message}", "错误",
                              MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        /// <summary>
        /// 拍照并进行检测 - 异步版本
        /// </summary>
        private async void TakePhotoAndCheck()
        {
            var totalStopwatch = Stopwatch.StartNew();

            try
            {
                if (_processingManager == null || !_processingManager.IsReady)
                {
                    MessageBox.Show("图像处理引擎未就绪，请检查模型文件", "检测失败",
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 并行拍照 - 在子线程中同时获取两个相机的图像
                var cameraStopwatch = Stopwatch.StartNew();
                var imageTask1 = Task.Run(() => CameraManager.Instance.TakePhoto(0));
                var imageTask2 = Task.Run(() => CameraManager.Instance.TakePhoto(1));

                var images = await Task.WhenAll(imageTask1, imageTask2);
                var image1 = images[0];
                var image2 = images[1];
                cameraStopwatch.Stop();
                Logger.Info($"并行拍照耗时：{cameraStopwatch.ElapsedMilliseconds}ms");

                if (image1 == null || image2 == null)
                {
                    MessageBox.Show($"拍照失败 - 相机1：{(image1 != null ? "成功" : "失败")}，相机2：{(image2 != null ? "成功" : "失败")}\n请确保预览已开启",
                                  "检测失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                Logger.Info("开始双相机图像检测");

                // UI清理 - 异步执行
                SafeUpdateUI(() => ClearAnnotatedImages());

                // 图像处理和推理 - 异步执行
                var processingStopwatch = Stopwatch.StartNew();
                var summary = await _processingManager.ProcessDualCameraImagesAsync(image1, image2);
                processingStopwatch.Stop();
                Logger.Info($"异步图像处理和推理耗时：{processingStopwatch.ElapsedMilliseconds}ms");

                // 更新检测结果和统计
                UpdateDetectionResult(summary.OverallResult);

                // UI更新计时
                var uiUpdateStopwatch = Stopwatch.StartNew();
                SafeUpdateUI(() =>
                {
                    string resultText = $"{summary.ResultMessage} | 耗时：{summary.TotalProcessingTime:F2}ms";

                    // 在PictureBox3和PictureBox4中显示带有缺陷标注的图像
                    DisplayAnnotatedImages(summary);

                    // 更新角度显示
                    UpdateAngleDisplay(summary);
                });
                uiUpdateStopwatch.Stop();
                Logger.Info($"UI结果更新耗时：{uiUpdateStopwatch.ElapsedMilliseconds}ms");

                // 详细结果生成和显示计时
                var detailStopwatch = Stopwatch.StartNew();
                string detailMessage = $"检测完成！\n\n" +
                                     $"综合结果：{(summary.OverallResult ? "OK" : "NG")}\n" +
                                     $"相机1：{summary.Camera1Result.Detections.Count}个目标\n" +
                                     $"相机2：{summary.Camera2Result.Detections.Count}个目标\n" +
                                     $"处理耗时：{summary.TotalProcessingTime:F2}ms\n\n" +
                                     $"检测详情：\n{GetDetectionDetails(summary)}";

                SafeUpdateUI(() =>
                {
                    tb_lsjllist.Text = detailMessage + "\r\n" + tb_lsjllist.Text;
                    tb_lsjl.Text = detailMessage;

                    // 根据检测结果设置文本颜色
                    if (summary.OverallResult)
                    {
                        tb_lsjl.ForeColor = Color.White; // OK时显示白色
                    }
                    else
                    {
                        tb_lsjl.ForeColor = Color.Red; // NG时显示红色
                    }

                    // tb_lsjllist.Refresh();
                    tb_lsjllist.ScrollToCaret();
                });
                detailStopwatch.Stop();
                Logger.Info($"详细结果生成和显示耗时：{detailStopwatch.ElapsedMilliseconds}ms");

                Logger.Info(detailMessage);

                Logger.Info($"双相机检测完成：{summary.ResultMessage}");

                // 停止总计时并输出各环节耗时统计
                totalStopwatch.Stop();

                string timingReport = $"=== 异步耗时统计报告 ===\n" +
                                    $"并行拍照：{cameraStopwatch.ElapsedMilliseconds}ms\n" +
                                    $"异步图像处理和推理：{processingStopwatch.ElapsedMilliseconds}ms\n" +
                                    $"UI结果更新：{uiUpdateStopwatch.ElapsedMilliseconds}ms\n" +
                                    $"详细结果生成和显示：{detailStopwatch.ElapsedMilliseconds}ms\n" +
                                    $"总耗时：{totalStopwatch.ElapsedMilliseconds}ms\n" +
                                    $"========================";

                Logger.Info(timingReport);
                Console.WriteLine(timingReport);

                // 如果检测失败，异步发送MQTT控制消息
                //if (!summary.OverallResult)
                //{
                //    Logger.Info("检测失败，将在5秒后异步发送MQTT控制消息");
                //    _ = Task.Run(async () =>
                //    {
                //        await Task.Delay(5000); // 异步延迟5秒
                //        var mqttSendResult = await SendDetectionFailureControlMessagesAsync();
                //        if (!mqttSendResult)
                //        {
                //            Logger.Warning("检测失败控制消息发送失败");
                //        }
                //    });
                //}
            }
            catch (Exception ex)
            {
                totalStopwatch.Stop();
                Logger.Exception(ex, $"拍照检测过程中发生异常:{ex.Message}，总耗时：{totalStopwatch.ElapsedMilliseconds}ms");
                SafeUpdateUI(() =>
                {
                    MessageBox.Show($"检测过程中发生异常：{ex.Message}", "错误",
                                  MessageBoxButtons.OK, MessageBoxIcon.Error);
                });
            }
        }

        private async Task TakePhotoAndCheckAsync()
        {
            try
            {
                if (_processingManager == null || !_processingManager.IsReady)
                {
                    MessageBox.Show("图像处理引擎未就绪，请检查模型文件", "检测失败",
                                  MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // 获取两个相机的图像
                var image1 = cameraManager?.TakePhoto(0);
                var image2 = cameraManager?.TakePhoto(1);

                if (image1 == null || image2 == null)
                {
                    MessageBox.Show($"拍照失败 - 相机1：{(image1 != null ? "成功" : "失败")}，相机2：{(image2 != null ? "成功" : "失败")}\n请确保预览已开启",
                                  "检测失败", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                Logger.Info("开始双相机图像检测");

                // 清空之前的检测结果显示
                SafeUpdateUI(() => ClearAnnotatedImages());

                // 使用图像处理管理器进行检测
                var summary = _processingManager.ProcessDualCameraImages(image1, image2);

                // 更新检测结果和统计
                UpdateDetectionResult(summary.OverallResult);

                // 更新检测结果显示 - 使用线程安全的UI更新
                SafeUpdateUI(() =>
                {
                    string resultText = $"{summary.ResultMessage} | 耗时：{summary.TotalProcessingTime:F2}ms";

                    // 在PictureBox3和PictureBox4中显示带有缺陷标注的图像
                    DisplayAnnotatedImages(summary);

                    // 更新角度显示
                    UpdateAngleDisplay(summary);

                    // 根据检测结果设置文本颜色
                    if (summary.OverallResult)
                    {
                        tb_lsjl.ForeColor = Color.White; // OK时显示白色
                    }
                    else
                    {
                        tb_lsjl.ForeColor = Color.Red; // NG时显示红色
                    }
                });

                // 显示详细结果
                string detailMessage = $"检测完成！\n\n" +
                                     $"综合结果：{(summary.OverallResult ? "OK" : "NG")}\n" +
                                     $"相机1：{summary.Camera1Result.Detections.Count}个目标\n" +
                                     $"相机2：{summary.Camera2Result.Detections.Count}个目标\n" +
                                     $"处理耗时：{summary.TotalProcessingTime:F2}ms\n\n" +
                                     $"检测详情：\n{GetDetectionDetails(summary)}";

                Logger.Info(detailMessage);

                Logger.Info($"双相机检测完成：{summary.ResultMessage}");
                //if (!summary.OverallResult)
                //{
                //    // 延迟5秒后发送检测失败的MQTT控制消息
                //    Logger.Info("检测失败，将在5秒后发送MQTT控制消息");
                //    await Task.Delay(5000); // 延迟5秒
                //    // 发送检测失败的MQTT控制消息（异步版本）
                //    var mqttSendResult = await SendDetectionFailureControlMessagesAsync();
                //    if (!mqttSendResult)
                //    {
                //        Logger.Warning("检测失败控制消息发送失败");
                //    }
                //}

            }
            catch (Exception ex)
            {
                Logger.Exception(ex, $"拍照检测过程中发生异常:{ex.Message}");
                SafeUpdateUI(() =>
                {
                    MessageBox.Show($"检测过程中发生异常：{ex.Message}", "错误",
                                  MessageBoxButtons.OK, MessageBoxIcon.Error);
                });
            }
        }

        /// <summary>
        /// 发送检测失败的MQTT控制消息（同步版本）
        /// </summary>
        /// <returns>发送结果</returns>
        private bool SendDetectionFailureControlMessages()
        {
            try
            {
                if (mqttManager == null)
                {
                    Logger.Warning("MQTT管理器未初始化，无法发送控制消息");
                    return false;
                }

                if (!mqttManager.IsConnected)
                {
                    Logger.Warning("MQTT未连接，无法发送控制消息");
                    return false;
                }

                Logger.Info("开始发送检测失败控制消息序列（同步版本）");

                // 使用助手类发送控制消息序列（同步版本）
                var (firstSuccess, secondSuccess) = MqttControlMessageHelper
                    .SendDetectionFailureControlSequenceAsync(mqttManager).GetAwaiter().GetResult();

                if (firstSuccess && secondSuccess)
                {
                    Logger.Info("检测失败控制消息序列发送成功");
                    return true;
                }
                else
                {
                    Logger.Warning($"检测失败控制消息序列发送部分失败 - 第一条: {firstSuccess}, 第二条: {secondSuccess}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, $"发送检测失败控制消息时发生异常: {ex.Message}");
                return false;
            }
        }



        /// <summary>
        /// 发送检测失败的MQTT控制消息
        /// </summary>
        /// <returns>发送结果</returns>
        private async Task<bool> SendDetectionFailureControlMessagesAsync()
        {
            try
            {
                if (mqttManager == null)
                {
                    Logger.Warn("MQTT管理器未初始化，无法发送控制消息");
                    return false;
                }

                if (!mqttManager.IsConnected)
                {
                    Logger.Warning("MQTT未连接，无法发送控制消息");
                    return false;
                }

                Logger.Info("开始发送检测失败控制消息序列");

                // 使用助手类发送控制消息序列
                var (firstSuccess, secondSuccess) = await MqttControlMessageHelper
                    .SendDetectionFailureControlSequenceAsync(mqttManager);

                if (firstSuccess && secondSuccess)
                {
                    Logger.Info("检测失败控制消息序列发送成功");
                    return true;
                }
                else
                {
                    Logger.Warning($"检测失败控制消息序列发送部分失败 - 第一条: {firstSuccess}, 第二条: {secondSuccess}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, $"发送检测失败控制消息时发生异常: {ex.Message}");
                return false;
            }
        }


        /// <summary>
        /// 清空PictureBox3和PictureBox4中的图像
        /// </summary>
        private void ClearAnnotatedImages()
        {
            try
            {
                SafeUpdateUI(() =>
                {
                    // 清空PictureBox3
                    pictureBox3.Image?.Dispose();
                    pictureBox3.Image = null;

                    // 清空PictureBox4
                    pictureBox4.Image?.Dispose();
                    pictureBox4.Image = null;
                });

                Logger.Debug("已清空检测结果图像显示");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "清空标注图像时发生异常");
            }
        }

        /// <summary>
        /// 线程安全的UI更新方法
        /// </summary>
        /// <param name="action">要执行的UI更新操作</param>
        private void SafeUpdateUI(Action action)
        {
            if (this.InvokeRequired)
            {
                this.Invoke(action);
            }
            else
            {
                action();
            }
        }

        /// <summary>
        /// 在PictureBox3和PictureBox4中显示带有缺陷标注的图像
        /// </summary>
        /// <param name="summary">检测结果汇总</param>
        private void DisplayAnnotatedImages(DetectionSummary summary)
        {
            try
            {
                // 显示相机1的检测结果图像到PictureBox3
                if (summary.Camera1Result?.ProcessedImage != null)
                {
                    var processedImage1 = summary.Camera1Result.ProcessedImage;
                    var bitmap1 = OpenCvSharp.Extensions.BitmapConverter.ToBitmap(processedImage1);

                    // 释放之前的图像
                    pictureBox3.Image?.Dispose();
                    pictureBox3.Image = bitmap1;
                    pictureBox3.SizeMode = PictureBoxSizeMode.Zoom;

                    Logger.Info($"相机1检测结果图像已显示到PictureBox3，检测到{summary.Camera1Result.Detections.Count}个目标");
                }
                else
                {
                    Logger.Warn("相机1没有可用的处理后图像");
                }

                // 显示相机2的检测结果图像到PictureBox4
                if (summary.Camera2Result?.ProcessedImage != null)
                {
                    var processedImage2 = summary.Camera2Result.ProcessedImage;
                    var bitmap2 = OpenCvSharp.Extensions.BitmapConverter.ToBitmap(processedImage2);

                    // 释放之前的图像
                    pictureBox4.Image?.Dispose();
                    pictureBox4.Image = bitmap2;
                    pictureBox4.SizeMode = PictureBoxSizeMode.Zoom;

                    Logger.Info($"相机2检测结果图像已显示到PictureBox4，检测到{summary.Camera2Result.Detections.Count}个目标");
                }
                else
                {
                    Logger.Warn("相机2没有可用的处理后图像");
                }
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "显示标注图像时发生异常");
                MessageBox.Show($"显示标注图像失败：{ex.Message}", "错误",
                              MessageBoxButtons.OK, MessageBoxIcon.Warning);
            }
        }

        /// <summary>
        /// 获取检测详情文本
        /// </summary>
        private string GetDetectionDetails(DetectionSummary summary)
        {
            var details = new StringBuilder();

            if (summary.Camera1Result != null)
            {
                details.AppendLine("相机1检测结果：");
                foreach (var detection in summary.Camera1Result.Detections)
                {
                    string detectionInfo = $"  - {detection.Key}: {detection.Value:P2}";

                    // 为COK2和FOK2添加角度信息
                    if ((detection.Key.ToLower() == "cok2" || detection.Key.ToLower() == "fok2") &&
                        summary.Camera1Result.PinInfos?.Count > 0)
                    {
                        var angles = summary.Camera1Result.PinInfos
                            .Where(p => p.Angle > 0)
                            .Select(p => p.Angle.ToString("F1") + "°")
                            .ToArray();

                        if (angles.Length > 0)
                        {
                            detectionInfo += $" [角度: {string.Join(", ", angles)}]";
                        }
                    }

                    details.AppendLine(detectionInfo);
                }
            }

            if (summary.Camera2Result != null)
            {
                details.AppendLine("相机2检测结果：");
                foreach (var detection in summary.Camera2Result.Detections)
                {
                    string detectionInfo = $"  - {detection.Key}: {detection.Value:P2}";

                    // 为COK2和FOK2添加角度信息
                    if ((detection.Key.ToLower() == "cok2" || detection.Key.ToLower() == "fok2") &&
                        summary.Camera2Result.PinInfos?.Count > 0)
                    {
                        var angles = summary.Camera2Result.PinInfos
                            .Where(p => p.Angle > 0)
                            .Select(p => p.Angle.ToString("F1") + "°")
                            .ToArray();

                        if (angles.Length > 0)
                        {
                            detectionInfo += $" [角度: {string.Join(", ", angles)}]";
                        }
                    }

                    details.AppendLine(detectionInfo);
                }
            }

            return details.ToString();
        }

        /// <summary>
        /// 更新角度显示信息
        /// </summary>
        private void UpdateAngleDisplay(DetectionSummary summary)
        {
            try
            {
                Logger.Info($"开始更新角度显示 - 相机1 PinInfos数量: {summary.Camera1Result?.PinInfos?.Count ?? 0}, 相机2 PinInfos数量: {summary.Camera2Result?.PinInfos?.Count ?? 0}");

                var angleInfo = new StringBuilder();
                bool hasAngles = false;

                // 收集相机1的角度信息
                if (summary.Camera1Result?.PinInfos?.Count > 0)
                {
                    Logger.Info($"相机1发现PinInfos：{summary.Camera1Result.PinInfos.Count}个，角度值：{string.Join(", ", summary.Camera1Result.PinInfos.Select(p => p.Angle.ToString("F1")))}");

                    var validAngles = summary.Camera1Result.PinInfos;//.Where(p => p.Angle > 0).ToList();
                    if (validAngles.Count > 0)
                    {
                        var detectionType = summary.Camera1Result.Detections
                            .Where(d => d.Key.ToLower() == "cok2" || d.Key.ToLower() == "fok2")
                            .FirstOrDefault().Key ?? "未知";

                        Logger.Info($"相机1有效角度：{validAngles.Count}个，检测类型：{detectionType}");

                        angleInfo.AppendLine($"相机1 {detectionType}:");
                        foreach (var pin in validAngles)
                        {
                            angleInfo.AppendLine($"  角度: {pin.Angle:F1}°");
                        }
                        hasAngles = true;
                    }
                    else
                    {
                        Logger.Warn("相机1所有角度值都<=0，被过滤掉了");
                    }
                }
                else
                {
                    Logger.Info("相机1没有PinInfos或PinInfos为空");
                }

                // 收集相机2的角度信息
                if (summary.Camera2Result?.PinInfos?.Count > 0)
                {
                    Logger.Info($"相机2发现PinInfos：{summary.Camera2Result.PinInfos.Count}个，角度值：{string.Join(", ", summary.Camera2Result.PinInfos.Select(p => p.Angle.ToString("F1")))}");

                    var validAngles = summary.Camera2Result.PinInfos;
                    if (validAngles.Count > 0)
                    {
                        var detectionType = summary.Camera2Result.Detections
                            .Where(d => d.Key.ToLower() == "cok2" || d.Key.ToLower() == "fok2")
                            .FirstOrDefault().Key ?? "未知";

                        Logger.Info($"相机2有效角度：{validAngles.Count}个，检测类型：{detectionType}");

                        if (hasAngles) angleInfo.AppendLine();
                        angleInfo.AppendLine($"相机2 {detectionType}:");
                        foreach (var pin in validAngles)
                        {
                            angleInfo.AppendLine($"  角度: {pin.Angle:F1}°");
                        }
                        hasAngles = true;
                    }
                    else
                    {
                        Logger.Warn("相机2所有角度值都<=0，被过滤掉了");
                    }
                }
                else
                {
                    Logger.Info("相机2没有PinInfos或PinInfos为空");
                }

                // 添加针脚倾斜检测结果
                angleInfo.AppendLine("--- 针脚倾斜检测结果 ---");
                var pinTiltResultText = summary.PinTiltResult.IsOk ? "OK" : "【NG】";
                angleInfo.AppendLine($"• 检测结果：{pinTiltResultText}");
                angleInfo.AppendLine($"• 检测耗时：{summary.PinTiltResult.ProcessingTime}ms");
                if (!string.IsNullOrEmpty(summary.PinTiltResult.Message))
                {
                    angleInfo.AppendLine($"• 详细信息：{summary.PinTiltResult.Message}");
                }

                // 更新UI显示 - 在tb_lsjl中显示角度信息
                if (hasAngles || summary.PinTiltResult != null)
                {
                    var currentText = tb_lsjl.Text;
                    if (!string.IsNullOrEmpty(currentText))
                    {
                        tb_lsjl.Text = currentText + "\n\n--- 角度检测结果 ---\n" + angleInfo.ToString();
                    }
                    else
                    {
                        tb_lsjl.Text = "--- 角度检测结果 ---\n" + angleInfo.ToString();
                    }
                }

                Logger.Info($"角度显示更新完成，检测到{(hasAngles ? "有" : "无")}角度信息");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "更新角度显示时发生异常");
            }
        }

        /// <summary>
        /// 窗体关闭时释放资源
        /// </summary>
        protected override void OnFormClosed(FormClosedEventArgs e)
        {
            try
            {
                Logger.Info("开始释放主窗体资源");

                // 释放异步管理器
                _asyncBusinessManager?.Dispose();

                // 释放图像处理管理器
                _processingManager?.Dispose();

                // 释放MQTT管理器
                mqttManager?.Dispose();

                // 释放相机管理器
                CameraManager.Instance?.Dispose();

                Logger.Info("主窗体资源释放完成");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "释放主窗体资源时发生异常");
            }
            finally
            {
                base.OnFormClosed(e);
            }
        }

        /// <summary>
        /// 更新统计显示
        /// </summary>
        private void UpdateStatisticsDisplay()
        {
            SafeUpdateUI(() =>
            {
                lblTotalCountValue.Text = totalCount.ToString();
                lblPassCountValue.Text = passCount.ToString();
                lblFailCountValue.Text = failCount.ToString();

                double passRate = totalCount > 0 ? (double)passCount / totalCount * 100 : 0;
                double failRate = totalCount > 0 ? (double)failCount / totalCount * 100 : 0;

                lblPassRateValue.Text = $"{passRate:F1}%";
                lblFailRateValue.Text = $"{failRate:F1}%";
            });
        }

        /// <summary>
        /// 更新检测结果并统计
        /// </summary>
        /// <param name="isPass">检测是否通过</param>
        private void UpdateDetectionResult(bool isPass)
        {
            totalCount++;
            if (isPass)
            {
                passCount++;
            }
            else
            {
                failCount++;
            }

            // 更新结果显示颜色
            SafeUpdateUI(() =>
            {
                if (isPass)
                {
                    lb_jg.Text = "OK";
                    lb_jg.ForeColor = Color.White;
                    lb_jg.BackColor = Color.Green;
                }
                else
                {
                    lb_jg.Text = "NG";
                    lb_jg.ForeColor = Color.White;
                    lb_jg.BackColor = Color.Red;
                }
            });

            // 更新统计显示
            UpdateStatisticsDisplay();

            // 保存统计数据
            SaveStatisticsData();
        }

        /// <summary>
        /// 清零按钮点击事件
        /// </summary>
        private void BtnClearStats_Click(object? sender, EventArgs e)
        {
            totalCount = 0;
            passCount = 0;
            failCount = 0;

            UpdateStatisticsDisplay();

            // 清空结果显示
            SafeUpdateUI(() =>
            {
                lb_jg.Text = "";
                lb_jg.BackColor = Color.Transparent;
            });

            // 保存统计数据
            SaveStatisticsData();
        }

        /// <summary>
        /// 加载统计数据
        /// </summary>
        private void LoadStatisticsData()
        {
            try
            {
                if (File.Exists(statisticsFilePath))
                {
                    string jsonContent = File.ReadAllText(statisticsFilePath);
                    var data = JsonConvert.DeserializeObject<StatisticsData>(jsonContent);
                    if (data != null)
                    {
                        totalCount = data.TotalCount;
                        passCount = data.PassCount;
                        failCount = data.FailCount;
                        Logger.Info($"已加载统计数据：总数={totalCount}, 合格={passCount}, 不良={failCount}");
                    }
                }
                else
                {
                    Logger.Info("统计数据文件不存在，使用默认值");
                }
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "加载统计数据失败");
                // 使用默认值
                totalCount = 0;
                passCount = 0;
                failCount = 0;
            }
        }

        /// <summary>
        /// 保存统计数据
        /// </summary>
        private void SaveStatisticsData()
        {
            try
            {
                var data = new StatisticsData
                {
                    TotalCount = totalCount,
                    PassCount = passCount,
                    FailCount = failCount,
                    LastUpdated = DateTime.Now
                };

                string jsonContent = JsonConvert.SerializeObject(data, Formatting.Indented);
                File.WriteAllText(statisticsFilePath, jsonContent);
                Logger.Debug($"已保存统计数据：总数={totalCount}, 合格={passCount}, 不良={failCount}");
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "保存统计数据失败");
            }
        }

        /// <summary>
        /// 执行预热检测 - 不统计结果，仅用于预热系统
        /// </summary>
        private async Task PerformWarmupDetection()
        {
            try
            {
                Logger.Info("=== 开始预热检测 ===");
                var warmupStopwatch = Stopwatch.StartNew();

                // 检查处理管理器是否就绪
                if (_processingManager == null || !_processingManager.IsReady)
                {
                    Logger.Warning("图像处理引擎未就绪，跳过预热检测");
                    return;
                }

                // 尝试获取相机图像进行预热
                var (image1, image2) = await CameraManager.Instance.TakeSensorTriggeredPhotosAsync();

                if (image1 == null || image2 == null)
                {
                    Logger.Warning("预热检测：无法获取相机图像，可能相机未连接");
                    return;
                }

                // 执行图像处理预热
                var summary = await _processingManager.ProcessDualCameraImagesAsync(image1, image2);

                warmupStopwatch.Stop();
                Logger.Info($"=== 预热检测完成，耗时：{warmupStopwatch.ElapsedMilliseconds}ms ===");
                Logger.Info($"预热检测结果：{(summary.OverallResult ? "OK" : "NG")}（此结果不计入统计）");

                // 释放预热检测的图像资源
                image1?.Dispose();
                image2?.Dispose();
            }
            catch (Exception ex)
            {
                Logger.Exception(ex, "预热检测过程中发生异常");
            }
        }

    }
}