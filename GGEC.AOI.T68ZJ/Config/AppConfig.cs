using GGEC.AOI.T68ZJ.Managers;
using System.Linq;

namespace GGEC.AOI.T68ZJ.Config
{
    /// <summary>
    /// 应用程序配置 - 使用静态变量，避免配置文件同步问题
    /// </summary>
    public static class AppConfig
    {
        // 相机管理器配置
        public static class CameraManager
        {
            public static CameraManagerFactory.CameraManagerType Type { get; set; } = CameraManagerFactory.CameraManagerType.Original;
        }

        // 相机配置
        public static class Camera
        {
            public static double ExposureTime { get; set; } = 2000.0;
            public static double Gain { get; set; } = 20.0;
            public static string PixelFormat { get; set; } = "Mono8";
            public static bool AutoExposureEnabled { get; set; } = false;
            public static bool AutoGainEnabled { get; set; } = false;
        }

        // 传送带配置
        public static class Conveyor
        {
            public static double Speed { get; set; } = 0.3;
            public static int TriggerDelayCompensation { get; set; } = 50;
            public static int MinPhotoIntervalMs { get; set; } = 10;
        }

        // 图像配置
        public static class Image
        {
            public static string OutputDirectory { get; set; } = "Images";
            public static string DefaultFormat { get; set; } = "Jpeg";
            public static int JpegQuality { get; set; } = 90;
        }

        // 日志配置
        public static class Log
        {
            public static string Level { get; set; } = "Info";
            public static bool EnableFileLogging { get; set; } = true;
        }

        // 网络配置
        public static class Network
        {
            public static string PacketSize { get; set; } = "auto";
            public static int HeartbeatTimeout { get; set; } = 3000;
        }

        // 性能配置
        public static class Performance
        {
            public static int PreviewFrameRate { get; set; } = 10;
            public static int MaxMemoryUsageMB { get; set; } = 1024;
        }

        // MQTT配置
        public static class Mqtt
        {
            public static string ServerEndpoint { get; set; } = "192.168.104.188";
            public static int ServerPort { get; set; } = 1884;
            public static string ClientId { get; set; } = "pc_exe_001";
            public static string UploadTopic { get; set; } = "/UploadData";
        }

        // 推理配置
        public static class Inference
        {
            public static string ModelPath { get; set; } = ".\\Resources\\Models\\t68zj_0723_1145_best.onnx";
            public static string SegmentModelPath { get; set; } = ".\\Resources\\Models\\t68zj_pin_0723_1511_best.onnx";
            public static string FOKModelPath { get; set; } = ".\\Resources\\Models\\t68zj_fok_0729_1947_best.onnx";
            public static double ConfidenceThreshold { get; set; } = 0.5;
            public static bool SaveResultImage { get; set; } = true;
            public static bool SaveCroppedDetections { get; set; } = true;
            public static bool SaveOriginImage { get; set; } = true;
            public static string[] CropLabels { get; set; } = { "cok2", "fok2" };
            public static bool OptimizeImageConversion { get; set; } = false;
            public static bool EnableDetailedTiming { get; set; } = false;
            public static bool SkipUnnecessaryOperations { get; set; } = false;
        }
    }

    /// <summary>
    /// 简单配置类（向后兼容）
    /// </summary>
    public static class SimpleConfig
    {
        /// <summary>
        /// 加载推理配置
        /// </summary>
        public static Managers.InferenceConfig LoadConfig()
        {
            return new Managers.InferenceConfig
            {
                ModelPath = AppConfig.Inference.ModelPath,
                SegmentModelPath = AppConfig.Inference.SegmentModelPath,
                FOKModelPath = AppConfig.Inference.FOKModelPath,
                ConfidenceThreshold = (float)AppConfig.Inference.ConfidenceThreshold,
                SaveResultImage = AppConfig.Inference.SaveResultImage,
                SaveCroppedDetections = AppConfig.Inference.SaveCroppedDetections,
                SaveOriginImage = AppConfig.Inference.SaveOriginImage,
                CropLabels = AppConfig.Inference.CropLabels.ToList(),
                OptimizeImageConversion = AppConfig.Inference.OptimizeImageConversion,
                EnableDetailedTiming = AppConfig.Inference.EnableDetailedTiming,
                SkipUnnecessaryOperations = AppConfig.Inference.SkipUnnecessaryOperations
            };
        }
    }
}
