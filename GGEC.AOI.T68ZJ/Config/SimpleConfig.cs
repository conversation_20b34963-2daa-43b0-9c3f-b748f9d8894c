using GGEC.AOI.T68ZJ.Managers;
using System.Collections.Generic;
using System.IO;
using System.Text.Json;

namespace GGEC.AOI.T68ZJ.Config
{
    /// <summary>
    /// 统一的应用程序配置管理器
    /// 替代App.config，使用JSON格式存储所有配置
    /// </summary>
    public static class AppConfig
    {
        private static readonly string ConfigPath = "Config/app_config.json";
        private static ApplicationConfig? _cachedConfig;
        private static readonly object _lock = new object();

        /// <summary>
        /// 加载应用程序配置
        /// </summary>
        public static ApplicationConfig LoadConfig()
        {
            if (_cachedConfig != null)
                return _cachedConfig;

            lock (_lock)
            {
                if (_cachedConfig != null)
                    return _cachedConfig;

                try
                {
                    if (File.Exists(ConfigPath))
                    {
                        var json = File.ReadAllText(ConfigPath);
                        _cachedConfig = JsonSerializer.Deserialize<ApplicationConfig>(json) ?? new ApplicationConfig();
                    }
                    else
                    {
                        // 创建默认配置
                        _cachedConfig = new ApplicationConfig();
                        SaveConfig(_cachedConfig);
                    }
                }
                catch
                {
                    _cachedConfig = new ApplicationConfig();
                }

                return _cachedConfig;
            }
        }

        /// <summary>
        /// 保存应用程序配置
        /// </summary>
        public static void SaveConfig(ApplicationConfig config)
        {
            try
            {
                var directory = Path.GetDirectoryName(ConfigPath);
                if (!string.IsNullOrEmpty(directory) && !Directory.Exists(directory))
                {
                    Directory.CreateDirectory(directory);
                }

                var json = JsonSerializer.Serialize(config, new JsonSerializerOptions
                {
                    WriteIndented = true
                });
                File.WriteAllText(ConfigPath, json);

                // 更新缓存
                lock (_lock)
                {
                    _cachedConfig = config;
                }
            }
            catch
            {
                // 忽略保存错误
            }
        }

        /// <summary>
        /// 重新加载配置（清除缓存）
        /// </summary>
        public static void ReloadConfig()
        {
            lock (_lock)
            {
                _cachedConfig = null;
            }
        }
    }

    /// <summary>
    /// 简化的配置管理器（向后兼容）
    /// </summary>
    public static class SimpleConfig
    {
        /// <summary>
        /// 加载推理配置
        /// </summary>
        public static InferenceConfig LoadConfig()
        {
            return AppConfig.LoadConfig().Inference;
        }

        /// <summary>
        /// 保存推理配置
        /// </summary>
        public static void SaveConfig(InferenceConfig config)
        {
            var appConfig = AppConfig.LoadConfig();
            appConfig.Inference = config;
            AppConfig.SaveConfig(appConfig);
        }
    }

    /// <summary>
    /// 完整的应用程序配置类
    /// 包含所有原App.config中的配置项
    /// </summary>
    public class ApplicationConfig
    {
        /// <summary>
        /// 相机管理器配置
        /// </summary>
        public CameraManagerConfig CameraManager { get; set; } = new CameraManagerConfig();

        /// <summary>
        /// 相机参数配置
        /// </summary>
        public CameraConfig Camera { get; set; } = new CameraConfig();

        /// <summary>
        /// 传送带相关配置
        /// </summary>
        public ConveyorConfig Conveyor { get; set; } = new ConveyorConfig();

        /// <summary>
        /// 图像保存配置
        /// </summary>
        public ImageConfig Image { get; set; } = new ImageConfig();

        /// <summary>
        /// 日志配置
        /// </summary>
        public LogConfig Log { get; set; } = new LogConfig();

        /// <summary>
        /// 网络配置（GigE设备）
        /// </summary>
        public NetworkConfig Network { get; set; } = new NetworkConfig();

        /// <summary>
        /// 性能配置
        /// </summary>
        public PerformanceConfig Performance { get; set; } = new PerformanceConfig();

        /// <summary>
        /// 推理配置
        /// </summary>
        public InferenceConfig Inference { get; set; } = new InferenceConfig();

        /// <summary>
        /// MQTT配置
        /// </summary>
        public MqttConfig Mqtt { get; set; } = new MqttConfig();
    }

    /// <summary>
    /// 相机管理器配置
    /// </summary>
    public class CameraManagerConfig
    {
        /// <summary>
        /// 相机管理器类型：Original 或 Enhanced
        /// </summary>
        public string Type { get; set; } = "Original";

        /// <summary>
        /// 获取相机管理器类型枚举
        /// </summary>
        public CameraManagerFactory.CameraManagerType GetManagerType()
        {
            return Type.ToLower() switch
            {
                "enhanced" => CameraManagerFactory.CameraManagerType.Enhanced,
                _ => CameraManagerFactory.CameraManagerType.Original
            };
        }

        /// <summary>
        /// 设置相机管理器类型
        /// </summary>
        public void SetManagerType(CameraManagerFactory.CameraManagerType type)
        {
            Type = type switch
            {
                CameraManagerFactory.CameraManagerType.Enhanced => "Enhanced",
                _ => "Original"
            };
        }
    }

    /// <summary>
    /// 相机参数配置
    /// </summary>
    public class CameraConfig
    {
        public double ExposureTime { get; set; } = 2000.0;
        public double Gain { get; set; } = 20.0;
        public string PixelFormat { get; set; } = "Mono8";
        public bool AutoExposureEnabled { get; set; } = false;
        public bool AutoGainEnabled { get; set; } = false;
    }

    /// <summary>
    /// 传送带相关配置
    /// </summary>
    public class ConveyorConfig
    {
        public double Speed { get; set; } = 0.3;
        public int TriggerDelayCompensation { get; set; } = 50;
        public int MinPhotoIntervalMs { get; set; } = 10;
    }

    /// <summary>
    /// 图像保存配置
    /// </summary>
    public class ImageConfig
    {
        public string OutputDirectory { get; set; } = "Images";
        public string DefaultFormat { get; set; } = "Jpeg";
        public int JpegQuality { get; set; } = 90;
    }

    /// <summary>
    /// 日志配置
    /// </summary>
    public class LogConfig
    {
        public string Level { get; set; } = "Info";
        public bool EnableFileLogging { get; set; } = true;
    }

    /// <summary>
    /// 网络配置（GigE设备）
    /// </summary>
    public class NetworkConfig
    {
        public string PacketSize { get; set; } = "auto";
        public int HeartbeatTimeout { get; set; } = 3000;
    }

    /// <summary>
    /// 性能配置
    /// </summary>
    public class PerformanceConfig
    {
        public int PreviewFrameRate { get; set; } = 10;
        public int MaxMemoryUsageMB { get; set; } = 1024;
    }

    /// <summary>
    /// MQTT配置
    /// </summary>
    public class MqttConfig
    {
        public string ServerEndpoint { get; set; } = "192.168.104.188";
        public int ServerPort { get; set; } = 1884;
        public string ClientId { get; set; } = "pc_exe_001";
        public string UploadTopic { get; set; } = "/UploadData";
    }
}
