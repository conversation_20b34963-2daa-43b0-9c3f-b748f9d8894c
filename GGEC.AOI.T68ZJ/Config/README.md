# 配置系统说明

## 概述

本项目已将原来的 `App.config` XML 配置文件替换为基于 JSON 的配置系统，提供更好的可读性、可维护性和灵活性。

## 配置文件位置

- **主配置文件**: `Config/app_config.json`
- **推理配置文件**: `Config/inference_config.json` (向后兼容)

## 配置结构

### 主配置文件 (app_config.json)

```json
{
  "CameraManager": {
    "Type": "Original"  // 或 "Enhanced"
  },
  "Camera": {
    "ExposureTime": 2000.0,
    "Gain": 20.0,
    "PixelFormat": "Mono8",
    "AutoExposureEnabled": false,
    "AutoGainEnabled": false
  },
  "Conveyor": {
    "Speed": 0.3,
    "TriggerDelayCompensation": 50,
    "MinPhotoIntervalMs": 10
  },
  "Image": {
    "OutputDirectory": "Images",
    "DefaultFormat": "Jpeg",
    "JpegQuality": 90
  },
  "Log": {
    "Level": "Info",
    "EnableFileLogging": true
  },
  "Network": {
    "PacketSize": "auto",
    "HeartbeatTimeout": 3000
  },
  "Performance": {
    "PreviewFrameRate": 10,
    "MaxMemoryUsageMB": 1024
  },
  "Mqtt": {
    "ServerEndpoint": "192.168.104.188",
    "ServerPort": 1884,
    "ClientId": "pc_exe_001",
    "UploadTopic": "/UploadData"
  },
  "Inference": {
    "ModelPath": ".\\Resources\\Models\\t68zj_0723_1145_best.onnx",
    "ConfidenceThreshold": 0.5,
    "SaveResultImage": true,
    // ... 其他推理配置
  }
}
```

## 使用方法

### 1. 加载配置

```csharp
// 加载完整应用配置
var appConfig = AppConfig.LoadConfig();

// 向后兼容：仅加载推理配置
var inferenceConfig = SimpleConfig.LoadConfig();
```

### 2. 修改配置

```csharp
var config = AppConfig.LoadConfig();
config.Camera.ExposureTime = 3000.0;
config.CameraManager.SetManagerType(CameraManagerFactory.CameraManagerType.Enhanced);
AppConfig.SaveConfig(config);
```

### 3. 重新加载配置

```csharp
AppConfig.ReloadConfig(); // 清除缓存
var newConfig = AppConfig.LoadConfig(); // 重新从文件加载
```

## 配置项说明

### CameraManager
- `Type`: 相机管理器类型，可选值：`"Original"` 或 `"Enhanced"`

### Camera
- `ExposureTime`: 曝光时间（微秒）
- `Gain`: 增益值
- `PixelFormat`: 像素格式，如 `"Mono8"`
- `AutoExposureEnabled`: 是否启用自动曝光
- `AutoGainEnabled`: 是否启用自动增益

### Conveyor
- `Speed`: 传送带速度（米/秒）
- `TriggerDelayCompensation`: 传感器触发延迟补偿（毫秒）
- `MinPhotoIntervalMs`: 最小拍照间隔（毫秒）

### Image
- `OutputDirectory`: 图像输出目录
- `DefaultFormat`: 默认图像格式
- `JpegQuality`: JPEG 质量（1-100）

### Log
- `Level`: 日志级别，如 `"Info"`, `"Debug"`, `"Error"`
- `EnableFileLogging`: 是否启用文件日志

### Network
- `PacketSize`: 网络包大小，可设为 `"auto"` 或具体数值
- `HeartbeatTimeout`: 心跳超时时间（毫秒）

### Performance
- `PreviewFrameRate`: 预览帧率
- `MaxMemoryUsageMB`: 最大内存使用量（MB）

### MQTT
- `ServerEndpoint`: MQTT 服务器地址
- `ServerPort`: MQTT 服务器端口
- `ClientId`: MQTT 客户端 ID
- `UploadTopic`: 上传数据主题

## 测试配置系统

可以使用 `ConfigTest` 类来测试配置系统：

```csharp
// 测试配置加载和保存
ConfigTest.TestConfigSystem();

// 显示当前配置
ConfigTest.ShowCurrentConfig();

// 重置为默认配置
ConfigTest.ResetToDefaults();
```

## 迁移说明

原 `App.config` 文件已被移除，所有配置项都已迁移到 JSON 格式。如果需要恢复原配置值，请参考上述配置结构手动设置。

## 优势

1. **可读性**: JSON 格式比 XML 更简洁易读
2. **类型安全**: 强类型配置类，编译时检查
3. **缓存机制**: 配置加载后会缓存，提高性能
4. **向后兼容**: 保持 `SimpleConfig` 接口不变
5. **灵活性**: 易于扩展新的配置项
6. **版本控制友好**: JSON 格式更适合版本控制
