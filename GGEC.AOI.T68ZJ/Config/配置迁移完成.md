# 配置迁移完成 - 从配置文件到静态变量

## 迁移概述

已成功将应用程序配置从JSON配置文件迁移到静态变量，解决了配置文件同步问题，便于前期调试。

## 主要变更

### 1. 创建了新的静态配置类
- **文件**: `GGEC.AOI.T68ZJ/Config/AppConfig.cs`
- **结构**: 使用静态嵌套类组织配置
- **优势**: 编译时检查，无需文件同步，便于调试修改

### 2. 配置结构
```csharp
public static class AppConfig
{
    public static class Camera { ... }      // 相机配置
    public static class Conveyor { ... }    // 传送带配置  
    public static class Image { ... }       // 图像配置
    public static class Log { ... }         // 日志配置
    public static class Network { ... }     // 网络配置
    public static class Performance { ... } // 性能配置
    public static class Mqtt { ... }        // MQTT配置
    public static class Inference { ... }   // 推理配置
}
```

### 3. 删除的文件
- `GGEC.AOI.T68ZJ/Config/SimpleConfig.cs` (旧版本)
- `GGEC.AOI.T68ZJ/Config/app_config.json`
- `GGEC.AOI.T68ZJ/Config/README.md`

### 4. 更新的文件
- `MainForm.cs` - 移除配置对象依赖，直接使用静态配置
- `CameraManagerFactory.cs` - 简化初始化方法
- `CameraManager.cs` - 更新配置加载方法
- `CameraManager2.cs` - 更新配置加载方法

## 使用方式

### 修改配置值
直接在代码中修改静态属性：
```csharp
// 修改相机曝光时间
AppConfig.Camera.ExposureTime = 3000.0;

// 修改MQTT服务器
AppConfig.Mqtt.ServerEndpoint = "192.168.1.100";
```

### 访问配置值
```csharp
// 获取相机配置
var exposureTime = AppConfig.Camera.ExposureTime;
var gain = AppConfig.Camera.Gain;

// 获取MQTT配置
var mqttServer = AppConfig.Mqtt.ServerEndpoint;
var mqttPort = AppConfig.Mqtt.ServerPort;
```

## 向后兼容性

保持了 `SimpleConfig.LoadConfig()` 方法的兼容性，返回适合 `ImageProcessingManager` 的配置对象。

## 编译状态

✅ **编译成功** - 项目现在可以正常编译，只有一些可忽略的警告。

## 优势

1. **无文件同步问题** - 配置直接在代码中，避免了配置文件更新不同步的问题
2. **便于调试** - 可以直接在代码中修改配置值进行测试
3. **编译时检查** - 配置错误在编译时就能发现
4. **类型安全** - 强类型配置，避免运行时类型错误
5. **简化部署** - 不需要额外的配置文件

## 注意事项

- 配置值现在是硬编码在代码中，如需运行时动态修改，可以直接设置静态属性
- 如果需要持久化配置更改，需要额外实现保存机制
- 生产环境如需要配置文件，可以在此基础上添加配置文件加载逻辑
