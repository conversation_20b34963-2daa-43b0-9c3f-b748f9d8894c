{"CameraManager": {"Type": "Original"}, "Camera": {"ExposureTime": 2000.0, "Gain": 20.0, "PixelFormat": "Mono8", "AutoExposureEnabled": false, "AutoGainEnabled": false}, "Conveyor": {"Speed": 0.3, "TriggerDelayCompensation": 50, "MinPhotoIntervalMs": 10}, "Image": {"OutputDirectory": "Images", "DefaultFormat": "Jpeg", "JpegQuality": 90}, "Log": {"Level": "Info", "EnableFileLogging": true}, "Network": {"PacketSize": "auto", "HeartbeatTimeout": 3000}, "Performance": {"PreviewFrameRate": 10, "MaxMemoryUsageMB": 1024}, "Inference": {"ModelPath": ".\\Resources\\Models\\t68zj_0723_1145_best.onnx", "SegmentModelPath": ".\\Resources\\Models\\t68zj_pin_0723_1511_best.onnx", "FOKModelPath": ".\\Resources\\Models\\t68zj_fok_0729_1947_best.onnx", "ConfidenceThreshold": 0.5, "SaveResultImage": true, "SaveCroppedDetections": true, "SaveOriginImage": true, "CropLabels": ["cok2", "fok2"], "OptimizeImageConversion": false, "EnableDetailedTiming": false, "SkipUnnecessaryOperations": false}, "Mqtt": {"ServerEndpoint": "***************", "ServerPort": 1884, "ClientId": "pc_exe_001", "UploadTopic": "/UploadData"}}